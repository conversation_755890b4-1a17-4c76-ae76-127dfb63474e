/**
 * tdesign v1.14.2
 * (c) 2025 tdesign
 * @license MIT
 */

'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var props = {
  align: {
    type: String,
    "default": "left",
    validator: function validator(val) {
      if (!val) return true;
      return ["left", "center", "right"].includes(val);
    }
  },
  allowInputOverMax: Boolean,
  autoWidth: <PERSON><PERSON>an,
  autocomplete: {
    type: String,
    "default": void 0
  },
  autofocus: <PERSON><PERSON><PERSON>,
  borderless: <PERSON><PERSON>an,
  clearable: <PERSON><PERSON>an,
  disabled: {
    type: Boolean,
    "default": void 0
  },
  format: {
    type: Function
  },
  inputClass: {
    type: [String, Object, Array]
  },
  label: {
    type: [String, Function]
  },
  maxcharacter: {
    type: Number
  },
  maxlength: {
    type: [String, Number]
  },
  name: {
    type: String,
    "default": ""
  },
  placeholder: {
    type: String,
    "default": void 0
  },
  prefixIcon: {
    type: Function
  },
  readonly: {
    type: <PERSON><PERSON><PERSON>,
    "default": void 0
  },
  showClearIconOnEmpty: <PERSON><PERSON><PERSON>,
  showLimitNumber: Boolean,
  size: {
    type: String,
    "default": "medium",
    validator: function validator(val) {
      if (!val) return true;
      return ["small", "medium", "large"].includes(val);
    }
  },
  spellCheck: Boolean,
  status: {
    type: String,
    "default": "default",
    validator: function validator(val) {
      if (!val) return true;
      return ["default", "success", "warning", "error"].includes(val);
    }
  },
  suffix: {
    type: [String, Function]
  },
  suffixIcon: {
    type: Function
  },
  tips: {
    type: [String, Function]
  },
  type: {
    type: String,
    "default": "text",
    validator: function validator(val) {
      if (!val) return true;
      return ["text", "number", "url", "tel", "password", "search", "submit", "hidden"].includes(val);
    }
  },
  value: {
    type: [String, Number],
    "default": void 0
  },
  modelValue: {
    type: [String, Number],
    "default": void 0
  },
  defaultValue: {
    type: [String, Number]
  },
  onBlur: Function,
  onChange: Function,
  onClear: Function,
  onClick: Function,
  onCompositionend: Function,
  onCompositionstart: Function,
  onEnter: Function,
  onFocus: Function,
  onKeydown: Function,
  onKeypress: Function,
  onKeyup: Function,
  onMouseenter: Function,
  onMouseleave: Function,
  onPaste: Function,
  onValidate: Function,
  onWheel: Function
};

exports["default"] = props;
//# sourceMappingURL=props.js.map
