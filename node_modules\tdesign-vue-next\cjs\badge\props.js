/**
 * tdesign v1.14.2
 * (c) 2025 tdesign
 * @license MIT
 */

'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var props = {
  color: {
    type: String,
    "default": ""
  },
  content: {
    type: [String, Function]
  },
  count: {
    type: [String, Number, Function],
    "default": 0
  },
  "default": {
    type: [String, Function]
  },
  dot: <PERSON>olean,
  maxCount: {
    type: Number,
    "default": 99
  },
  offset: {
    type: Array
  },
  shape: {
    type: String,
    "default": "circle",
    validator: function validator(val) {
      return ["circle", "round"].includes(val);
    }
  },
  showZero: Boolean,
  size: {
    type: String,
    "default": "medium",
    validator: function validator(val) {
      return ["small", "medium"].includes(val);
    }
  }
};

exports["default"] = props;
//# sourceMappingURL=props.js.map
