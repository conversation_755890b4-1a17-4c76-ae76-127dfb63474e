declare const _default: import("vue").DefineComponent<{
    abridgeName: {
        type: import("vue").PropType<import("./type").TdUploadProps["abridgeName"]>;
    };
    accept: {
        type: StringConstructor;
        default: string;
    };
    action: {
        type: StringConstructor;
        default: string;
    };
    allowUploadDuplicateFile: BooleanConstructor;
    autoUpload: {
        type: BooleanConstructor;
        default: boolean;
    };
    beforeAllFilesUpload: {
        type: import("vue").PropType<import("./type").TdUploadProps["beforeAllFilesUpload"]>;
    };
    beforeUpload: {
        type: import("vue").PropType<import("./type").TdUploadProps["beforeUpload"]>;
    };
    cancelUploadButton: {
        type: import("vue").PropType<import("./type").TdUploadProps["cancelUploadButton"]>;
    };
    data: {
        type: import("vue").PropType<import("./type").TdUploadProps["data"]>;
    };
    default: {
        type: import("vue").PropType<import("./type").TdUploadProps["default"]>;
    };
    disabled: {
        type: BooleanConstructor;
        default: any;
    };
    dragContent: {
        type: import("vue").PropType<import("./type").TdUploadProps["dragContent"]>;
    };
    draggable: {
        type: BooleanConstructor;
        default: any;
    };
    fileListDisplay: {
        type: import("vue").PropType<import("./type").TdUploadProps["fileListDisplay"]>;
    };
    files: {
        type: import("vue").PropType<import("./type").TdUploadProps["files"]>;
        default: import("./type").TdUploadProps["files"];
    };
    defaultFiles: {
        type: import("vue").PropType<import("./type").TdUploadProps["defaultFiles"]>;
        default: () => import("./type").TdUploadProps["defaultFiles"];
    };
    format: {
        type: import("vue").PropType<import("./type").TdUploadProps["format"]>;
    };
    formatRequest: {
        type: import("vue").PropType<import("./type").TdUploadProps["formatRequest"]>;
    };
    formatResponse: {
        type: import("vue").PropType<import("./type").TdUploadProps["formatResponse"]>;
    };
    headers: {
        type: import("vue").PropType<import("./type").TdUploadProps["headers"]>;
    };
    imageViewerProps: {
        type: import("vue").PropType<import("./type").TdUploadProps["imageViewerProps"]>;
    };
    inputAttributes: {
        type: import("vue").PropType<import("./type").TdUploadProps["inputAttributes"]>;
    };
    isBatchUpload: BooleanConstructor;
    locale: {
        type: import("vue").PropType<import("./type").TdUploadProps["locale"]>;
    };
    max: {
        type: NumberConstructor;
        default: number;
    };
    method: {
        type: import("vue").PropType<import("./type").TdUploadProps["method"]>;
        default: import("./type").TdUploadProps["method"];
        validator(val: import("./type").TdUploadProps["method"]): boolean;
    };
    mockProgressDuration: {
        type: NumberConstructor;
    };
    multiple: BooleanConstructor;
    name: {
        type: StringConstructor;
        default: string;
    };
    placeholder: {
        type: StringConstructor;
        default: string;
    };
    requestMethod: {
        type: import("vue").PropType<import("./type").TdUploadProps["requestMethod"]>;
    };
    showImageFileName: {
        type: BooleanConstructor;
        default: boolean;
    };
    showThumbnail: BooleanConstructor;
    showUploadProgress: {
        type: BooleanConstructor;
        default: boolean;
    };
    sizeLimit: {
        type: import("vue").PropType<import("./type").TdUploadProps["sizeLimit"]>;
    };
    status: {
        type: import("vue").PropType<import("./type").TdUploadProps["status"]>;
        validator(val: import("./type").TdUploadProps["status"]): boolean;
    };
    theme: {
        type: import("vue").PropType<import("./type").TdUploadProps["theme"]>;
        default: import("./type").TdUploadProps["theme"];
        validator(val: import("./type").TdUploadProps["theme"]): boolean;
    };
    tips: {
        type: import("vue").PropType<import("./type").TdUploadProps["tips"]>;
    };
    trigger: {
        type: import("vue").PropType<import("./type").TdUploadProps["trigger"]>;
    };
    triggerButtonProps: {
        type: import("vue").PropType<import("./type").TdUploadProps["triggerButtonProps"]>;
    };
    uploadAllFilesInOneRequest: BooleanConstructor;
    uploadButton: {
        type: import("vue").PropType<import("./type").TdUploadProps["uploadButton"]>;
    };
    uploadPastedFiles: {
        type: BooleanConstructor;
        default: boolean;
    };
    useMockProgress: {
        type: BooleanConstructor;
        default: boolean;
    };
    value: {
        type: import("vue").PropType<import("./type").TdUploadProps["value"]>;
        default: import("./type").TdUploadProps["value"];
    };
    modelValue: {
        type: import("vue").PropType<import("./type").TdUploadProps["value"]>;
        default: import("./type").TdUploadProps["value"];
    };
    defaultValue: {
        type: import("vue").PropType<import("./type").TdUploadProps["defaultValue"]>;
        default: () => import("./type").TdUploadProps["defaultValue"];
    };
    withCredentials: BooleanConstructor;
    onCancelUpload: import("vue").PropType<import("./type").TdUploadProps["onCancelUpload"]>;
    onChange: import("vue").PropType<import("./type").TdUploadProps["onChange"]>;
    onDragenter: import("vue").PropType<import("./type").TdUploadProps["onDragenter"]>;
    onDragleave: import("vue").PropType<import("./type").TdUploadProps["onDragleave"]>;
    onDrop: import("vue").PropType<import("./type").TdUploadProps["onDrop"]>;
    onFail: import("vue").PropType<import("./type").TdUploadProps["onFail"]>;
    onOneFileFail: import("vue").PropType<import("./type").TdUploadProps["onOneFileFail"]>;
    onOneFileSuccess: import("vue").PropType<import("./type").TdUploadProps["onOneFileSuccess"]>;
    onPreview: import("vue").PropType<import("./type").TdUploadProps["onPreview"]>;
    onProgress: import("vue").PropType<import("./type").TdUploadProps["onProgress"]>;
    onRemove: import("vue").PropType<import("./type").TdUploadProps["onRemove"]>;
    onSelectChange: import("vue").PropType<import("./type").TdUploadProps["onSelectChange"]>;
    onSuccess: import("vue").PropType<import("./type").TdUploadProps["onSuccess"]>;
    onValidate: import("vue").PropType<import("./type").TdUploadProps["onValidate"]>;
    onWaitingUploadFilesChange: import("vue").PropType<import("./type").TdUploadProps["onWaitingUploadFilesChange"]>;
}, () => JSX.Element, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{
    abridgeName: {
        type: import("vue").PropType<import("./type").TdUploadProps["abridgeName"]>;
    };
    accept: {
        type: StringConstructor;
        default: string;
    };
    action: {
        type: StringConstructor;
        default: string;
    };
    allowUploadDuplicateFile: BooleanConstructor;
    autoUpload: {
        type: BooleanConstructor;
        default: boolean;
    };
    beforeAllFilesUpload: {
        type: import("vue").PropType<import("./type").TdUploadProps["beforeAllFilesUpload"]>;
    };
    beforeUpload: {
        type: import("vue").PropType<import("./type").TdUploadProps["beforeUpload"]>;
    };
    cancelUploadButton: {
        type: import("vue").PropType<import("./type").TdUploadProps["cancelUploadButton"]>;
    };
    data: {
        type: import("vue").PropType<import("./type").TdUploadProps["data"]>;
    };
    default: {
        type: import("vue").PropType<import("./type").TdUploadProps["default"]>;
    };
    disabled: {
        type: BooleanConstructor;
        default: any;
    };
    dragContent: {
        type: import("vue").PropType<import("./type").TdUploadProps["dragContent"]>;
    };
    draggable: {
        type: BooleanConstructor;
        default: any;
    };
    fileListDisplay: {
        type: import("vue").PropType<import("./type").TdUploadProps["fileListDisplay"]>;
    };
    files: {
        type: import("vue").PropType<import("./type").TdUploadProps["files"]>;
        default: import("./type").TdUploadProps["files"];
    };
    defaultFiles: {
        type: import("vue").PropType<import("./type").TdUploadProps["defaultFiles"]>;
        default: () => import("./type").TdUploadProps["defaultFiles"];
    };
    format: {
        type: import("vue").PropType<import("./type").TdUploadProps["format"]>;
    };
    formatRequest: {
        type: import("vue").PropType<import("./type").TdUploadProps["formatRequest"]>;
    };
    formatResponse: {
        type: import("vue").PropType<import("./type").TdUploadProps["formatResponse"]>;
    };
    headers: {
        type: import("vue").PropType<import("./type").TdUploadProps["headers"]>;
    };
    imageViewerProps: {
        type: import("vue").PropType<import("./type").TdUploadProps["imageViewerProps"]>;
    };
    inputAttributes: {
        type: import("vue").PropType<import("./type").TdUploadProps["inputAttributes"]>;
    };
    isBatchUpload: BooleanConstructor;
    locale: {
        type: import("vue").PropType<import("./type").TdUploadProps["locale"]>;
    };
    max: {
        type: NumberConstructor;
        default: number;
    };
    method: {
        type: import("vue").PropType<import("./type").TdUploadProps["method"]>;
        default: import("./type").TdUploadProps["method"];
        validator(val: import("./type").TdUploadProps["method"]): boolean;
    };
    mockProgressDuration: {
        type: NumberConstructor;
    };
    multiple: BooleanConstructor;
    name: {
        type: StringConstructor;
        default: string;
    };
    placeholder: {
        type: StringConstructor;
        default: string;
    };
    requestMethod: {
        type: import("vue").PropType<import("./type").TdUploadProps["requestMethod"]>;
    };
    showImageFileName: {
        type: BooleanConstructor;
        default: boolean;
    };
    showThumbnail: BooleanConstructor;
    showUploadProgress: {
        type: BooleanConstructor;
        default: boolean;
    };
    sizeLimit: {
        type: import("vue").PropType<import("./type").TdUploadProps["sizeLimit"]>;
    };
    status: {
        type: import("vue").PropType<import("./type").TdUploadProps["status"]>;
        validator(val: import("./type").TdUploadProps["status"]): boolean;
    };
    theme: {
        type: import("vue").PropType<import("./type").TdUploadProps["theme"]>;
        default: import("./type").TdUploadProps["theme"];
        validator(val: import("./type").TdUploadProps["theme"]): boolean;
    };
    tips: {
        type: import("vue").PropType<import("./type").TdUploadProps["tips"]>;
    };
    trigger: {
        type: import("vue").PropType<import("./type").TdUploadProps["trigger"]>;
    };
    triggerButtonProps: {
        type: import("vue").PropType<import("./type").TdUploadProps["triggerButtonProps"]>;
    };
    uploadAllFilesInOneRequest: BooleanConstructor;
    uploadButton: {
        type: import("vue").PropType<import("./type").TdUploadProps["uploadButton"]>;
    };
    uploadPastedFiles: {
        type: BooleanConstructor;
        default: boolean;
    };
    useMockProgress: {
        type: BooleanConstructor;
        default: boolean;
    };
    value: {
        type: import("vue").PropType<import("./type").TdUploadProps["value"]>;
        default: import("./type").TdUploadProps["value"];
    };
    modelValue: {
        type: import("vue").PropType<import("./type").TdUploadProps["value"]>;
        default: import("./type").TdUploadProps["value"];
    };
    defaultValue: {
        type: import("vue").PropType<import("./type").TdUploadProps["defaultValue"]>;
        default: () => import("./type").TdUploadProps["defaultValue"];
    };
    withCredentials: BooleanConstructor;
    onCancelUpload: import("vue").PropType<import("./type").TdUploadProps["onCancelUpload"]>;
    onChange: import("vue").PropType<import("./type").TdUploadProps["onChange"]>;
    onDragenter: import("vue").PropType<import("./type").TdUploadProps["onDragenter"]>;
    onDragleave: import("vue").PropType<import("./type").TdUploadProps["onDragleave"]>;
    onDrop: import("vue").PropType<import("./type").TdUploadProps["onDrop"]>;
    onFail: import("vue").PropType<import("./type").TdUploadProps["onFail"]>;
    onOneFileFail: import("vue").PropType<import("./type").TdUploadProps["onOneFileFail"]>;
    onOneFileSuccess: import("vue").PropType<import("./type").TdUploadProps["onOneFileSuccess"]>;
    onPreview: import("vue").PropType<import("./type").TdUploadProps["onPreview"]>;
    onProgress: import("vue").PropType<import("./type").TdUploadProps["onProgress"]>;
    onRemove: import("vue").PropType<import("./type").TdUploadProps["onRemove"]>;
    onSelectChange: import("vue").PropType<import("./type").TdUploadProps["onSelectChange"]>;
    onSuccess: import("vue").PropType<import("./type").TdUploadProps["onSuccess"]>;
    onValidate: import("vue").PropType<import("./type").TdUploadProps["onValidate"]>;
    onWaitingUploadFilesChange: import("vue").PropType<import("./type").TdUploadProps["onWaitingUploadFilesChange"]>;
}>>, {
    name: string;
    value: import("./type").UploadFile[];
    disabled: boolean;
    action: string;
    multiple: boolean;
    max: number;
    draggable: boolean;
    method: "get" | "options" | "POST" | "GET" | "PUT" | "OPTIONS" | "PATCH" | "post" | "put" | "patch";
    withCredentials: boolean;
    files: import("./type").UploadFile[];
    useMockProgress: boolean;
    uploadAllFilesInOneRequest: boolean;
    isBatchUpload: boolean;
    allowUploadDuplicateFile: boolean;
    autoUpload: boolean;
    accept: string;
    defaultValue: import("./type").UploadFile[];
    placeholder: string;
    theme: "file" | "image" | "custom" | "file-input" | "file-flow" | "image-flow";
    modelValue: import("./type").UploadFile[];
    defaultFiles: import("./type").UploadFile[];
    uploadPastedFiles: boolean;
    showUploadProgress: boolean;
    showImageFileName: boolean;
    showThumbnail: boolean;
}, {}>;
export default _default;
