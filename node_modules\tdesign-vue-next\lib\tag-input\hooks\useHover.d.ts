export interface UseHoverParams {
    readonly: boolean;
    disabled: boolean;
    onMouseenter: (context: {
        e: MouseEvent;
    }) => void;
    onMouseleave: (context: {
        e: MouseEvent;
    }) => void;
}
export declare function useHover(props: UseHoverParams): {
    isHover: import("vue").Ref<boolean>;
    addHover: (context: {
        e: MouseEvent;
    }) => void;
    cancelHover: (context: {
        e: MouseEvent;
    }) => void;
};
