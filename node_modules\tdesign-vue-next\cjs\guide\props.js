/**
 * tdesign v1.14.2
 * (c) 2025 tdesign
 * @license MIT
 */

'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var props = {
  counter: {
    type: Function
  },
  current: {
    type: Number,
    "default": void 0
  },
  modelValue: {
    type: Number,
    "default": void 0
  },
  defaultCurrent: {
    type: Number
  },
  finishButtonProps: {
    type: Object
  },
  hideCounter: Boolean,
  hidePrev: Boolean,
  hideSkip: <PERSON>olean,
  highlightPadding: {
    type: Number,
    "default": 8
  },
  mode: {
    type: String,
    "default": "popup",
    validator: function validator(val) {
      if (!val) return true;
      return ["popup", "dialog"].includes(val);
    }
  },
  nextButtonProps: {
    type: Object
  },
  prevButtonProps: {
    type: Object
  },
  showOverlay: {
    type: Boolean,
    "default": true
  },
  skipButtonProps: {
    type: Object
  },
  steps: {
    type: Array
  },
  zIndex: {
    type: Number,
    "default": 999999
  },
  onChange: Function,
  onFinish: Function,
  onNextStepClick: Function,
  onPrevStepClick: Function,
  onSkip: Function
};

exports["default"] = props;
//# sourceMappingURL=props.js.map
