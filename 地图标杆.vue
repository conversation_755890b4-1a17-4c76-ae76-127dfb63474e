<template>
    <!-- 底层地图 -->
    <div class="map-container" id="cesiumContainer"></div>
    </template>
    
    <script setup>
    import * as Cesium from 'cesium'
    
    // 检查WebGL支持情况
    const checkWebGLSupport = () => {
      try {
        const canvas = document.createElement('canvas')
        return !!(window.WebGLRenderingContext && 
          (canvas.getContext('webgl') || canvas.getContext('experimental-webgl')))
      } catch (e) {
        return false
      }
    }
    
    // 显示备用地图（如果WebGL不可用）
    const showFallbackMap = () => {
      console.warn('WebGL不可用，显示备用地图')
      const mapContainer = document.getElementById('cesiumContainer')
      if (mapContainer) {
        mapContainer.style.backgroundColor = '#e0e0e0'
        mapContainer.innerHTML = `
          <div style="position:absolute;top:0;left:0;width:100%;height:100%;display:flex;flex-direction:column;justify-content:center;align-items:center;color:#666;">
            <div style="font-size:20px;margin-bottom:10px;">地图加载失败</div>
            <div>您的浏览器可能不支持WebGL或者已被禁用</div>
            <div style="margin-top:20px;">
              <button onclick="window.location.reload()" style="padding:8px 16px;background:#207FFF;color:#fff;border:none;border-radius:4px;cursor:pointer;">
                重新加载
              </button>
            </div>
          </div>
        `
      }
    }
    
    // 初始化Cesium地图
    const initCesiumMap = () => {
      try {
        // 设置Cesium Token
        Cesium.Ion.defaultAccessToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************.rP8t05HPVW0IyJn4Zbsa59qEht27DJSM5Qn9LcuLnVA'
        
        // 确保DOM元素已经加载
        setTimeout(() => {
          try {
            // 使用最简单的配置创建Cesium Viewer实例
            viewer.value = new Cesium.Viewer('cesiumContainer', {
              terrainProvider: new Cesium.EllipsoidTerrainProvider(),
              baseLayerPicker: false,
              geocoder: false,
              homeButton: false,
              infoBox: false,
              sceneModePicker: false,
              selectionIndicator: false,
              timeline: false,
              animation: false,
              fullscreenButton: false,
              scene3DOnly: false,
              imageryProvider: false,  // 禁用默认底图
              creditContainer: document.createElement('div')  // 隐藏版权信息
            });
          
            if (viewer.value) {
              console.log('Cesium Viewer创建成功');
              
              // 移除默认图层
              viewer.value.imageryLayers.removeAll();
              
              // 添加本地地图图层
              try {
                const tiandituImageryProvider = new Cesium.UrlTemplateImageryProvider({
                  url: 'http://127.0.0.1:8091/{z4490}/{x}/{y}.jpg',
                  tilingScheme: new Cesium.GeographicTilingScheme(),
                  customTags: {
                    z4490: function (imageryProvider, x, y, level) {
                      return level + 1;
                    }
                  },
                  maximumLevel: 12
                });
                
                viewer.value.imageryLayers.addImageryProvider(tiandituImageryProvider);
                
                console.log('本地地图加载成功');
                
                // 设置初始位置
                viewer.value.camera.flyTo({
                  destination: Cesium.Cartesian3.fromDegrees(112.40132, 34.624302, 50000),
                  orientation: {
                    heading: Cesium.Math.toRadians(0),
                    pitch: Cesium.Math.toRadians(-90),
                    roll: 0.0
                  }
                });
    
                // 设置地图拖放事件监听
                setupMapDropEvents();
                
                // 设置实体点击事件
                setupEntityClickEvents();
                
              } catch (error) {
                console.warn('加载本地地图失败，使用默认底图', error);
              }
            } else {
              console.error('Cesium Viewer创建失败');
            }
          } catch (innerError) {
            console.error('Cesium初始化内部错误', innerError);
          }
        }, 500); // 延迟500毫秒确保DOM已加载
      } catch (error) {
        console.error('初始化Cesium地图失败', error);
      }
    }
    
    </script>