<template>
  <div class="layout-container">
    <!-- 顶部导航栏 -->
    <header class="layout-header">
      <div class="header-content">
        <!-- 左侧：系统名称和菜单 -->
        <div class="header-left">
          <div class="system-name">空间服务与应用预测系统</div>
          <nav class="main-navigation">
            <div
              v-for="menu in mainMenus"
              :key="menu.path"
              class="nav-item"
              :class="{ active: isActiveMenu(menu.path) }"
              @mouseenter="handleMenuHover(menu)"
              @mouseleave="handleMenuLeave"
              @click="handleMenuClick(menu)"
            >
              <t-icon :name="menu.icon" class="nav-icon" />
              <span class="nav-text">{{ menu.title }}</span>

              <!-- 二级菜单下拉 -->
              <div
                v-if="hoveredMenu === menu.path && menu.children"
                class="sub-menu-dropdown"
                @mouseenter="keepDropdownOpen"
                @mouseleave="handleMenuLeave"
              >
                <div
                  v-for="subMenu in menu.children"
                  :key="subMenu.path"
                  class="sub-menu-item"
                  @click.stop="handleSubMenuClick(subMenu)"
                >
                  {{ subMenu.title }}
                </div>
              </div>
            </div>
          </nav>
        </div>
        
        <!-- 右侧：用户操作区 -->
        <div class="header-right">
          <!-- 消息通知 -->
          <t-dropdown :options="notificationOptions" @click="handleNotificationClick">
            <div class="header-action-item">
              <t-icon name="notification" />
              <t-badge v-if="unreadCount > 0" :count="unreadCount" />
            </div>
          </t-dropdown>
          
          <!-- 用户菜单 -->
          <t-dropdown :options="userMenuOptions" @click="handleUserMenuClick">
            <div class="header-action-item user-info">
              <t-avatar size="small">{{ userInfo.name.charAt(0) }}</t-avatar>
              <span class="username">{{ userInfo.name }}</span>
              <t-icon name="chevron-down" />
            </div>
          </t-dropdown>
        </div>
      </div>
    </header>
    
    <!-- 内容展示区 - 全屏地图 -->
    <main class="layout-main">
      <div class="map-main-content">
        <!-- Cesium三维地图容器 -->
        <div class="cesium-map-container" id="cesiumContainer"></div>

        <!-- 左上角三级菜单Tab区域 -->
        <div class="third-level-overlay" v-if="currentRoute">
          <!-- 三级菜单Tab -->
          <div class="third-level-tabs">
            <router-view v-slot="{ Component }">
              <transition name="fade" mode="out-in">
                <component :is="Component" />
              </transition>
            </router-view>
          </div>
        </div>
      </div>
    </main>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted, onUnmounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { MessagePlugin } from 'tdesign-vue-next'
import * as Cesium from 'cesium'

const router = useRouter()
const route = useRoute()

// 用户信息
const userInfo = ref({
  name: 'Admin',
  avatar: '',
  role: '系统管理员'
})

// 未读消息数量
const unreadCount = ref(3)

// 当前激活的主菜单和子菜单
const activeMainMenu = ref('')
const activeSubMenu = ref('')

// 鼠标悬停的菜单
const hoveredMenu = ref('')

// Cesium地图实例
const viewer = ref(null)

// 主菜单配置
const mainMenus = ref([
  {
    path: '/spatial-framework',
    title: '空间基准框架服务',
    icon: 'location',
    children: [
      { path: '/spatial-framework/dynamic-unification', title: '动态时空基准统一' },
      { path: '/spatial-framework/coordinate-transform', title: '区域独立坐标系转换' },
      { path: '/spatial-framework/elevation-unification', title: '国家高程基准统一' },
      { path: '/spatial-framework/polar-elevation', title: '极地高程基准统一' }
    ]
  },
  {
    path: '/data-application',
    title: '多源数据应用与预测',
    icon: 'chart-line',
    children: [
      { path: '/data-application/ionosphere-monitoring', title: '电离层监测与预测' },
      { path: '/data-application/troposphere-monitoring', title: '对流层监测与分析' },
      { path: '/data-application/sea-level-prediction', title: '区域海平面变化预测' }
    ]
  }
])

// 当前路由
const currentRoute = computed(() => {
  return route.path
})

// 通知菜单选项
const notificationOptions = ref([
  { content: '查看所有通知', value: 'all' },
  { content: '标记已读', value: 'read' }
])

// 用户菜单选项
const userMenuOptions = ref([
  { content: '个人设置', value: 'profile' },
  { content: '修改密码', value: 'password' },
  { content: '退出登录', value: 'logout' }
])

// 判断菜单是否激活
const isActiveMenu = (menuPath) => {
  return route.path.startsWith(menuPath)
}

// 主菜单点击处理
const handleMenuClick = (menu) => {
  if (menu.children && menu.children.length > 0) {
    // 如果有子菜单，跳转到第一个子菜单
    router.push(menu.children[0].path)
  } else {
    router.push(menu.path)
  }
}

// 鼠标悬停菜单处理
const handleMenuHover = (menu) => {
  hoveredMenu.value = menu.path
}

// 鼠标离开菜单处理
const handleMenuLeave = () => {
  setTimeout(() => {
    hoveredMenu.value = ''
  }, 100) // 延迟100ms，避免鼠标移动到下拉菜单时闪烁
}

// 保持下拉菜单打开
const keepDropdownOpen = () => {
  // 当鼠标在下拉菜单上时，保持菜单打开
}

// 子菜单点击处理
const handleSubMenuClick = (subMenu) => {
  router.push(subMenu.path)
  hoveredMenu.value = '' // 点击后关闭下拉菜单
}

// 子菜单切换处理
const handleSubMenuChange = (value) => {
  router.push(value)
}

// 通知点击处理
const handleNotificationClick = (data) => {
  switch (data.value) {
    case 'all':
      MessagePlugin.info('查看所有通知')
      break
    case 'read':
      unreadCount.value = 0
      MessagePlugin.success('已标记为已读')
      break
  }
}

// 用户菜单点击处理
const handleUserMenuClick = (data) => {
  switch (data.value) {
    case 'profile':
      MessagePlugin.info('个人设置功能开发中')
      break
    case 'password':
      MessagePlugin.info('修改密码功能开发中')
      break
    case 'logout':
      handleLogout()
      break
  }
}

// 退出登录
const handleLogout = () => {
  localStorage.removeItem('token')
  localStorage.removeItem('username')
  MessagePlugin.success('已退出登录')
  router.push('/login')
}

// 监听路由变化，更新激活状态
watch(route, (newRoute) => {
  // 更新激活的子菜单
  activeSubMenu.value = newRoute.path
  
  // 更新激活的主菜单
  for (const menu of mainMenus.value) {
    if (newRoute.path.startsWith(menu.path)) {
      activeMainMenu.value = menu.path
      break
    }
  }
}, { immediate: true })

// 检查WebGL支持情况
const checkWebGLSupport = () => {
  try {
    const canvas = document.createElement('canvas')
    return !!(window.WebGLRenderingContext &&
      (canvas.getContext('webgl') || canvas.getContext('experimental-webgl')))
  } catch (e) {
    return false
  }
}

// 显示备用地图（如果WebGL不可用）
const showFallbackMap = () => {
  console.warn('WebGL不可用，显示备用地图')
  const mapContainer = document.getElementById('cesiumContainer')
  if (mapContainer) {
    mapContainer.style.backgroundColor = '#e0e0e0'
    mapContainer.innerHTML = `
      <div style="position:absolute;top:0;left:0;width:100%;height:100%;display:flex;flex-direction:column;justify-content:center;align-items:center;color:#666;">
        <div style="font-size:20px;margin-bottom:10px;">地图加载失败</div>
        <div>您的浏览器可能不支持WebGL或者已被禁用</div>
        <div style="margin-top:20px;">
          <button onclick="window.location.reload()" style="padding:8px 16px;background:#063078;color:#fff;border:none;border-radius:4px;cursor:pointer;">
            重新加载
          </button>
        </div>
      </div>
    `
  }
}

// 初始化Cesium地图
const initCesiumMap = () => {
  try {
    // 检查WebGL支持
    if (!checkWebGLSupport()) {
      showFallbackMap()
      return
    }

    // 设置Cesium Token
    Cesium.Ion.defaultAccessToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************.rP8t05HPVW0IyJn4Zbsa59qEht27DJSM5Qn9LcuLnVA'

    // 确保DOM元素已经加载
    setTimeout(() => {
      try {
        // 使用最简单的配置创建Cesium Viewer实例
        viewer.value = new Cesium.Viewer('cesiumContainer', {
          terrainProvider: new Cesium.EllipsoidTerrainProvider(),
          baseLayerPicker: false,
          geocoder: false,
          homeButton: false,
          infoBox: false,
          sceneModePicker: false,
          selectionIndicator: false,
          timeline: false,
          animation: false,
          fullscreenButton: false,
          scene3DOnly: false,
          imageryProvider: false,  // 禁用默认底图
          creditContainer: document.createElement('div')  // 隐藏版权信息
        });

        if (viewer.value) {
          console.log('Cesium Viewer创建成功');

          // 移除默认图层
          viewer.value.imageryLayers.removeAll();

          // 添加本地地图图层
          try {
            const tiandituImageryProvider = new Cesium.UrlTemplateImageryProvider({
              url: 'http://127.0.0.1:8091/{z4490}/{x}/{y}.jpg',
              tilingScheme: new Cesium.GeographicTilingScheme(),
              customTags: {
                z4490: function (imageryProvider, x, y, level) {
                  return level + 1;
                }
              },
              maximumLevel: 12
            });

            viewer.value.imageryLayers.addImageryProvider(tiandituImageryProvider);

            console.log('本地地图加载成功');

            // 设置初始位置
            viewer.value.camera.flyTo({
              destination: Cesium.Cartesian3.fromDegrees(112.40132, 34.624302, 50000),
              orientation: {
                heading: Cesium.Math.toRadians(0),
                pitch: Cesium.Math.toRadians(-90),
                roll: 0.0
              }
            });

          } catch (error) {
            console.warn('加载本地地图失败，使用默认底图', error);
          }
        } else {
          console.error('Cesium Viewer创建失败');
        }
      } catch (innerError) {
        console.error('Cesium初始化内部错误', innerError);
        showFallbackMap()
      }
    }, 500); // 延迟500毫秒确保DOM已加载
  } catch (error) {
    console.error('初始化Cesium地图失败', error);
    showFallbackMap()
  }
}

// 组件挂载时初始化
onMounted(() => {
  // 获取用户信息
  const username = localStorage.getItem('username')
  if (username) {
    userInfo.value.name = username
  }

  // 初始化Cesium地图
  initCesiumMap()
})

// 组件卸载时清理
onUnmounted(() => {
  if (viewer.value) {
    viewer.value.destroy()
    viewer.value = null
  }
})
</script>

<style scoped>
.layout-container {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: var(--td-bg-color-page);
}

/* 顶部导航栏样式 */
.layout-header {
  height: 60px;
  background: var(--td-bg-color-container);
  border-bottom: 1px solid var(--td-border-level-1-color);
  box-shadow: var(--td-shadow-1);
  z-index: 1000;
}

.header-content {
  height: 100%;
  padding: 0 24px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 40px;
}

.system-name {
  font-size: 18px;
  font-weight: 600;
  color: var(--td-brand-color);
  white-space: nowrap;
}

.main-navigation {
  display: flex;
  gap: 8px;
}

.nav-item {
  position: relative;
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  border-radius: var(--td-radius-default);
  cursor: pointer;
  transition: all 0.2s;
  color: var(--td-text-color-primary);
}

.nav-item:hover {
  background: var(--td-bg-color-container-active);
  color: var(--td-brand-color);
}

.nav-item.active {
  background: var(--td-brand-color-light);
  color: var(--td-brand-color);
}

/* 二级菜单下拉样式 */
.sub-menu-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  min-width: 200px;
  background: var(--td-bg-color-container);
  border: 1px solid var(--td-border-level-1-color);
  border-radius: var(--td-radius-default);
  box-shadow: var(--td-shadow-2);
  z-index: 9999;
  padding: 8px 0;
  margin-top: 4px;
}

.sub-menu-item {
  padding: 8px 16px;
  color: var(--td-text-color-primary);
  cursor: pointer;
  transition: all 0.2s;
  font-size: 14px;
}

.sub-menu-item:hover {
  background: var(--td-bg-color-container-active);
  color: var(--td-brand-color);
}

.nav-icon {
  font-size: 16px;
}

.nav-text {
  font-size: 14px;
  font-weight: 500;
  white-space: nowrap;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 16px;
}

.header-action-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  border-radius: var(--td-radius-default);
  cursor: pointer;
  transition: all 0.2s;
  position: relative;
}

.header-action-item:hover {
  background: var(--td-bg-color-container-active);
}

.user-info {
  gap: 8px;
}

.username {
  font-size: 14px;
  color: var(--td-text-color-primary);
}

/* 主内容区样式 */
.layout-main {
  flex: 1;
  height: 1020px;
  overflow: hidden;
  position: relative;
}

.map-main-content {
  width: 100%;
  height: 100%;
  position: relative;
}

/* Cesium地图容器 */
.cesium-map-container {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
}

/* 左上角三级菜单覆盖层 */
.third-level-overlay {
  position: absolute;
  top: 10px;
  left: 10px;
  z-index: 1000;
  max-width: 800px;
  max-height: 80%;
}

.third-level-tabs {
  /* 三级菜单Tab容器样式由子组件控制 */
}

/* 页面切换动画 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .header-left {
    gap: 20px;
  }
  
  .nav-text {
    display: none;
  }
}

@media (max-width: 768px) {
  .system-name {
    font-size: 16px;
  }
  
  .main-navigation {
    gap: 4px;
  }
  
  .nav-item {
    padding: 6px 8px;
  }
}
</style>
