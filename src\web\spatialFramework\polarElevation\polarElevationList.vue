<template>
  <div class="polar-elevation-container">
    <div class="page-header">
      <h1 class="page-title">极地高程基准统一</h1>
      <p class="page-subtitle">针对极地区域，以极轨测高卫星观测数据为基础，实现整体和部分区域的高程基准转换统一</p>
    </div>
    
    <div class="content-area">
      <div class="card">
        <div class="card-header">
          <h3>极地高程基准功能</h3>
        </div>
        <div class="card-body">
          <div class="polar-features">
            <div class="feature-item">
              <div class="feature-icon">🛰️</div>
              <h4>极轨测高卫星数据</h4>
              <p>以极轨测高卫星观测数据为基础</p>
            </div>
            <div class="feature-item">
              <div class="feature-icon">📡</div>
              <h4>测高波形数据重跟踪</h4>
              <p>开展测高波形数据重跟踪、粗差剔除等预处理方法研究</p>
            </div>
            <div class="feature-item">
              <div class="feature-icon">🧊</div>
              <h4>冰盖表面高程分析</h4>
              <p>开展高程数据深度基准统一、冰盖表面高程/质量变化提取</p>
            </div>
            <div class="feature-item">
              <div class="feature-icon">🌊</div>
              <h4>海洋重力场模型</h4>
              <p>海洋重力场/海底地形模型反演等工作</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'

const pageData = ref({
  title: '极地高程基准统一',
  description: '针对极地区域，以极轨测高卫星观测数据为基础'
})

onMounted(() => {
  console.log('极地高程基准统一页面已加载')
})
</script>

<style scoped>
.polar-elevation-container {
  padding: 24px;
  background: var(--td-bg-color-page);
  min-height: 100%;
}

.page-header {
  margin-bottom: 24px;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: var(--td-text-color-primary);
  margin-bottom: 8px;
}

.page-subtitle {
  font-size: 14px;
  color: var(--td-text-color-secondary);
  line-height: 1.6;
}

.polar-features {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
}

.feature-item {
  padding: 20px;
  background: var(--td-bg-color-container);
  border-radius: var(--td-radius-medium);
  border: 1px solid var(--td-border-level-1-color);
  text-align: center;
  transition: all 0.3s ease;
}

.feature-item:hover {
  box-shadow: var(--td-shadow-2);
  transform: translateY(-2px);
}

.feature-icon {
  font-size: 32px;
  margin-bottom: 12px;
}

.feature-item h4 {
  font-size: 16px;
  font-weight: 600;
  color: var(--td-text-color-primary);
  margin-bottom: 8px;
}

.feature-item p {
  font-size: 14px;
  color: var(--td-text-color-secondary);
  line-height: 1.5;
}
</style>
