---
type: "always_apply"
---

#                              **项目整体规划**

## **项目布局**

* **整体系统的结构布局**：包含登录页、系统内容页面
  
  * **登录后系统页面布局**：  
    
    顶部导航栏+内容展示区 上下结构布局，顶部导航栏高度60px，内容区高度1020px；
    
    顶部导航栏：依次显示系统名称（空间服务与应用预测系统），一级菜单项， 右侧显示消息、用户图标，鼠标移入用户图标时悬浮显示修改密码、退出登录等功能项
    
    

## **技术、组件路线**

* **前端技术框架**：使用Vue。  
* **UI 组件库**：使用TDesign、Element UI。  
* **图表控件**：采用ECharts。  
* **地图组件**：三维地图使用本地cesium瓦片地图（具体参照“地图标杆.vue” 文件）。  
* **终端运行命令**：项目启动 npm run dev。

## **用色规范**

* **整体系统风格**：明确是浅色风格、深色风格或混合风格。  
* **主色调**：例如，主图色 \#063078。  
* **具体用色规范参照**：参照“整体用色规范-主题色-\#063078.css”文件。

## **项目文件命名、结构规范**

### **命名规范**

* **采用小驼峰法**：除了第一个单词外，其他单词的首字母大写。例如：helloWorld、helloPhp、myNameIsJohnDoe。  
* **首页**：index.vue  
* **大屏/一张图/态势图/地图**：overView.vue  
* **图表看板**：dashBoard.vue  
* **列表页**：\[模块名\]List.vue  
* **详情页**：\[模块名\]Detail.vue  
* **模态框**：\[功能名\]Modal.vue

### **目录规范**

* **层级划分**：以“平台-分系统-子系统-菜单-子菜单”的结构对文件夹结构进行划分。  
* **特殊文件夹**：  
  * 所有模态框内嵌页面存放在对应模块路径下的 Modal 文件夹中。  
  * 所有公用组件存放在对应模块路径下的 component 文件夹中。  
  * 所有页面模拟数据存放在对应模块路径下的 data 文件夹中。  
  * 所有ECharts图表组件存放在对应模块路径下的 echartsForm 文件夹中。

### **页面存放**

* src 文件夹作为项目源码根目录，下设 web 和 mobile 两个文件夹，对应Web端和移动端的页面存放路径。  
* 所有和管理端有关的页面文件，均存放在 ./src/web 文件夹下的对应英文名称子文件夹内。  
* 所有和移动端有关的页面文件，均存放在 ./src/mobile 文件夹下的对应英文名称子文件夹内。

### **公共资源**

* assets 文件夹为项目的共用资源文件夹，下设 css 文件夹、images 文件夹以及 icon 文件夹。  
* 所有和项目工程相关的自定义样式文件均存放在 ./assets/css 文件夹。  
* 所有和项目工程相关的可复用图片文件均存放在 ./assets/images 文件夹。  
* 所有和项目工程相关的可复用图标文件均存放在 ./assets/icon 文件夹。

## **通用组件和模态框**

### **组件和模态框说明**

* 所有模态框内嵌页面存放在对应模块路径下的 Modal 文件夹中。  
* 所有公用组件存放在对应模块路径下的 component 文件夹中。

### **主要模块设计模式**

系统主要采用列表页+功能页+模态框的设计模式，列表页用于数据管理和展示，功能页用于复杂操作，模态框用于表单输入和详情展示。

## **目录结构示例**

项目源码参考以下目录结构：

```
src/
├── web/                                        # Web端页面
│   ├── intelligenceEngine/                     # 情报时空交互分析引擎集成应用
│   │   ├── dataManagement/                     # 情报环境数据管理
│   │   │   ├── dataModelingManagement/         # 数据建模：情报环境一体化数据模型管理
│   │   │   │   ├── frameworkDescription.vue    # 框架描述：情报环境一体化描述框架概览
│   │   │   │   ├── dataStandardManagement.vue  # 数据标准管理：管理数据库表、字段命名规范等标准
│   │   │   │   ├── dataModeling.vue            # 数据建模：提供可视化数据模型设计工具
│   │   │   │   └── modelManagement.vue         # 数据模型管理：管理已创建的数据模型
│   │   │   ├── dataAccessList.vue              # 数据接入：支持从全单位情报数据资源池内读取
│   │   │   ├── dataProcessingList.vue          # 数据处理：数据预处理、清洗、转换等功能
│   │   │   ├── dataManagement/                 # 数据管理：数据维护与索引配置功能
│   │   │   │   ├── dataMantenanceList.vue      # 数据维护：支持对数据进行新增、修改、删除
│   │   │   │   └── indexConfigList.vue         # 索引配置：调整索引权重、同步周期等策略
│   │   │   ├── dataService/                    # 数据服务：提供四种数据服务功能
│   │   │   │   ├── sqlEngine.vue               # 数据库执行引擎：对不同数据源执行SQL语句调用
│   │   │   │   ├── apiService.vue              # API接口服务：提供接口管理、分类和状态控制
│   │   │   │   ├── offlinePackage.vue          # 离线数据包：支持创建、下载、预览数据包
│   │   │   │   └── onlineBrowse.vue            # 在线浏览：支持多数据源浏览和高级筛选
│   │   │   ├── component/                      # 情报环境数据管理公共组件
│   │   │   └── Modal/                          # 情报环境数据管理模态框
│   │   │
│   │   └── algorithmModel/                     # 算法模型管理
│   │       ├── modelPackagingList.vue          # 算法模型封装部署：打包、统一管理、部署功能
│   │       ├── modelServiceList.vue            # 算法模型服务管理：服务配置、注册、监控功能
│   │       ├── modelCatalogList.vue            # 算法模型服务目录：可视化注册、展示、检索功能
│   │       ├── component/                      # 算法模型管理公共组件
│   │       └── Modal/                          # 算法模型管理模态框
│   │
│   ├── visualizationManagement/                # 情报环境可视化管理
│   │   └── gridProcessing/                     # 情报环境网格化处理
│   │   │   ├── gridGenerationList.vue          # 情报网格生成：标准网格和六角格网络生成功能
│   │   │   ├── gridEncodingList.vue            # 情报战场网格编码：标准网格和六角格编码功能
│   │   │   ├── gridOperationList.vue           # 情报网格编码运算：网格编码的查询和运算功能
│   │   │   ├── dataOrganizationList.vue        # 情报数据网格组织：栅格、矢量和统计数据组织
│   │   │   ├── dataProcessingList.vue          # 情报数据网格处理：聚合、分解、抽稀功能
│   │   │   ├── dataExportList.vue              # 情报网格数据导出：多种格式的导出功能
│   │   │   ├── component/                      # 情报环境网格化处理公共组件
│   │   │   └── Modal/                          # 情报环境网格化处理模态框
│   │   │ 
│   │   └── visualAnalysis/                     # 情报环境可视分析
│   │       ├── gisPlattformOverView.vue        # 三维gis平台：预留功能，跳转gisroad.com
│   │       ├── integratedServiceOverView.vue   # 集成服务：预留功能，跳转baidu.com
│   │       ├── dataVisualizationDashBoard.vue  # 情报环境可视化：多维可视化展示，视图推荐功能
│   │       ├── component/                      # 情报环境可视分析公共组件
│   │       └── Modal/                          # 情报环境可视分析模态框
│   │
│   └── supportTools/                           # 情报时空网络开发支撑工具
│       ├── entityProcessing/                   # 情报环境实体化处理
│       │   ├── entityRecognitionList.vue       # 实体识别和关联：自动识别关联和手动标注功能
│       │   ├── normalizationList.vue           # 基准形态处理：命名规范化、单位标准化等功能
│       │   ├── abilityFeatureList.vue          # 能力特征处理：提取能力信息与实体分类功能
│       │   ├── correlationProcessingList.vue   # 关联影响处理：有向图构建、图分析挖掘功能
│       │   ├── component/                      # 情报环境实体化处理公共组件
│       │   └── Modal/                          # 情报环境实体化处理模态框
│       │
│       ├── spatiotemporalProcessing/           # 情报环境时空化处理
│       │   ├── featureRecognitionList.vue      # 时空特征识别：空间特征识别、时间特征识别功能
│       │   ├── entityProcessingList.vue        # 实体时空化处理：关键词匹配、时空化功能
│       │   ├── component/                      # 情报环境时空化处理公共组件
│       │   └── Modal/                          # 情报环境时空化处理模态框
│       │
│       │
│       ├── correlationProcessing/              # 情报环境关联化处理
│       │   ├── geometricRelationList.vue       # 几何关系特征处理：几何形态描述和关系处理
│       │   ├── spatialRelationList.vue         # 空间关系特征处理：拓扑关系、方向关系处理
│       │   ├── temporalRelationList.vue        # 时序关系特征处理：时序关系处理和可视化功能
│       │   ├── component/                      # 情报环境关联化处理公共组件
│       │   └── Modal/                          # 情报环境关联化处理模态框
│       │
│       └── knowledgeProcessing/                # 情报环境知识化处理
│           ├── knowledgeModelingList.vue       # 情报环境知识建模：可视化本体建模和管理功能
│           ├── knowledgeRepresentationList.vue # 情报环境知识表征：知识表达模型构建和存储功能
│           ├── knowledgeQueryList.vue          # 情报环境查询：索引构建、推理和API服务功能
│           ├── component/                      # 情报环境知识化处理公共组件
│           └── Modal/                          # 情报环境知识化处理模态框
│
└── assets/                                     # 公共资源
    ├── css/                                    # 样式文件
    │   ├── common.css                          # 通用样式
    │   ├── variables.css                       # 样式变量定义
    │   └── themes/                             # 主题样式
    │
    ├── images/                                 # 图片资源
    │   ├── logo.png                            # 系统Logo
    │   ├── icons/                              # 小图标
    │   └── backgrounds/                        # 背景图片
    │
    └── icon/                                   # 图标资源
        ├── common-icons.js                     # 通用图标集合
        └── special-icons.js                    # 特殊图标定义
```

## 子系统页面对应关系示例

### 1. 情报时空交互分析引擎集成应用 (intelligenceEngine)

#### 1.1 情报环境数据管理 (dataManagement)

| 功能模块               | 文件路径                                                     | 页面类型 | 业务描述                                              |
| ---------------------- | ------------------------------------------------------------ | -------- | ----------------------------------------------------- |
| 数据建模               |                                                              |          |                                                       |
| &emsp;- 框架描述       | web/intelligenceEngine/dataManagement/dataModelingManagement/frameworkDescription.vue | 功能页   | 展示情报环境一体化描述框架的概览和核心功能            |
| &emsp;- 数据标准管理   | web/intelligenceEngine/dataManagement/dataModelingManagement/dataStandardManagement.vue | 列表页   | 管理数据库表、字段命名规范、字段标准等数据标准        |
| &emsp;- 数据建模       | web/intelligenceEngine/dataManagement/dataModelingManagement/dataModeling.vue | 功能页   | 提供可视化数据模型设计工具，支持图形化创建数据模型    |
| &emsp;- 数据模型管理   | web/intelligenceEngine/dataManagement/dataModelingManagement/modelManagement.vue | 列表页   | 管理已创建的数据模型，支持导入导出和版本控制          |
| 数据接入               | web/intelligenceEngine/dataManagement/dataAccessList.vue     | 列表页   | 支持从全单位情报数据资源池内读取所需的情报环境数据    |
| 数据处理               | web/intelligenceEngine/dataManagement/dataProcessingList.vue | 列表页   | 支持数据预处理、清洗、标准转换和文本内容提取功能      |
| 数据管理               |                                                              |          |                                                       |
| &emsp;- 数据维护       | web/intelligenceEngine/dataManagement/dataMantenanceList.vue | 列表页   | 支持对数据进行新增、修改、删除等维护操作              |
| &emsp;- 索引配置       | web/intelligenceEngine/dataManagement/indexConfigList.vue    | 列表页   | 可视化调整索引权重、同步周期等检索策略                |
| 数据服务               |                                                              |          |                                                       |
| &emsp;- 数据库执行引擎 | web/intelligenceEngine/dataManagement/dataService/sqlEngine.vue | 功能页   | 支持通过统一规范的执行语句对不同数据源进行SQL执行调用 |
| &emsp;- API接口服务    | web/intelligenceEngine/dataManagement/dataService/apiService.vue | 功能页   | 提供接口管理、分类和状态控制功能                      |
| &emsp;- 离线数据包     | web/intelligenceEngine/dataManagement/dataService/offlinePackage.vue | 功能页   | 支持情报环境数据以离线数据包形式创建、下载和预览      |
| &emsp;- 在线浏览       | web/intelligenceEngine/dataManagement/dataService/onlineBrowse.vue | 功能页   | 支持情报环境数据的在线浏览和高级筛选功能              |

#### 1.2 算法模型管理 (algorithmModel)

| 功能模块         | 文件路径                                                     | 页面类型 | 业务描述                                         |
| ---------------- | ------------------------------------------------------------ | -------- | ------------------------------------------------ |
| 算法模型封装部署 | web/intelligenceEngine/algorithmModel/modelPackagingList.vue | 列表页   | 算法模型打包、统一管理、图形化部署和环境监控功能 |
| 算法模型服务管理 | web/intelligenceEngine/algorithmModel/modelServiceList.vue   | 列表页   | 服务配置管理、注册、状态监控、负载均衡和熔断功能 |
| 算法模型服务目录 | web/intelligenceEngine/algorithmModel/modelCatalogList.vue   | 列表页   | 服务目录的可视化注册、展示与检索功能             |

### ...................