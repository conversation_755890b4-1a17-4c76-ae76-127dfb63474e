/**
 * tdesign v1.14.2
 * (c) 2025 tdesign
 * @license MIT
 */

'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var props = {
  attach: {
    type: [String, Function],
    "default": ""
  },
  content: {
    type: [String, Function]
  },
  "default": {
    type: [String, Function]
  },
  delay: {
    type: Number,
    "default": 0
  },
  fullscreen: <PERSON>olean,
  indicator: {
    type: [Boolean, Function],
    "default": true
  },
  inheritColor: Boolean,
  loading: {
    type: <PERSON>olean,
    "default": true
  },
  preventScrollThrough: {
    type: Boolean,
    "default": true
  },
  showOverlay: {
    type: Boolean,
    "default": true
  },
  size: {
    type: String,
    "default": "medium"
  },
  text: {
    type: [String, Function]
  },
  zIndex: {
    type: Number
  }
};

exports["default"] = props;
//# sourceMappingURL=props.js.map
