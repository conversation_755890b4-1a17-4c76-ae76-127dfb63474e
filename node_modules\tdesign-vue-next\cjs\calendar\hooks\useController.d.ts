import { CalendarState } from '../types';
import { TdCalendarProps, ControllerOptions } from '../type';
export declare function userController(props: TdCalendarProps, state: CalendarState): {
    options: import("vue").ComputedRef<ControllerOptions>;
    configData: import("vue").ComputedRef<Record<string, any>>;
    visible: import("vue").ComputedRef<boolean>;
    checkControllerVisible: (name: string) => boolean;
    checkControllerDisabled: (name: string, propsName: string) => boolean;
    emitControllerChange: () => void;
};
