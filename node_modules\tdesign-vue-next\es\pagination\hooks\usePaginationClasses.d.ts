import { Ref } from 'vue';
import { TdPaginationProps } from '../type';
export declare function usePaginationClasses(props: TdPaginationProps, innerCurrent: Ref<number>, innerPageSize: Ref<number>, name: Ref<string>): {
    pageCount: import("vue").ComputedRef<number>;
    paginationClass: import("vue").ComputedRef<(string | {
        [x: string]: boolean;
    })[]>;
    totalClass: import("vue").ComputedRef<string[]>;
    sizerClass: import("vue").ComputedRef<string[]>;
    preBtnClass: import("vue").ComputedRef<(string | {
        [x: string]: boolean;
    })[]>;
    nextBtnClass: import("vue").ComputedRef<(string | {
        [x: string]: boolean;
    })[]>;
    btnWrapClass: import("vue").ComputedRef<string[]>;
    btnMoreClass: import("vue").ComputedRef<(string | {
        [x: string]: boolean;
    })[]>;
    jumperClass: import("vue").ComputedRef<string[]>;
    jumperInputClass: import("vue").ComputedRef<string[]>;
    simpleClass: import("vue").ComputedRef<string[]>;
    getButtonClass: (index: number) => (string | {
        [x: string]: boolean;
    })[];
};
