<template>
  <div class="ionosphere-monitoring-container">
    <div class="page-header">
      <h1 class="page-title">电离层监测与预测</h1>
      <p class="page-subtitle">从电离层监测、对流层监测和区域海平面基准归算及分析等关键技术攻关</p>
    </div>
    
    <div class="content-area">
      <div class="card">
        <div class="card-header">
          <h3>电离层监测功能</h3>
        </div>
        <div class="card-body">
          <div class="monitoring-features">
            <div class="feature-item">
              <div class="feature-icon">📡</div>
              <h4>区域电离层 TEC值提取</h4>
              <p>开展区域电离层 TEC值提取</p>
            </div>
            <div class="feature-item">
              <div class="feature-icon">📊</div>
              <h4>电离层行扰分析</h4>
              <p>进行电离层行扰分析和电离层不确定性分析</p>
            </div>
            <div class="feature-item">
              <div class="feature-icon">🔮</div>
              <h4>电离层短期预报</h4>
              <p>进行电离层短期预报</p>
            </div>
            <div class="feature-item">
              <div class="feature-icon">🎯</div>
              <h4>行扰监测与预测</h4>
              <p>实现电离层的行扰监测与预测</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'

const pageData = ref({
  title: '电离层监测与预测',
  description: '从电离层监测、对流层监测和区域海平面基准归算及分析等关键技术攻关'
})

onMounted(() => {
  console.log('电离层监测与预测页面已加载')
})
</script>

<style scoped>
.ionosphere-monitoring-container {
  padding: 24px;
  background: var(--td-bg-color-page);
  min-height: 100%;
}

.page-header {
  margin-bottom: 24px;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: var(--td-text-color-primary);
  margin-bottom: 8px;
}

.page-subtitle {
  font-size: 14px;
  color: var(--td-text-color-secondary);
  line-height: 1.6;
}

.monitoring-features {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
}

.feature-item {
  padding: 20px;
  background: var(--td-bg-color-container);
  border-radius: var(--td-radius-medium);
  border: 1px solid var(--td-border-level-1-color);
  text-align: center;
  transition: all 0.3s ease;
}

.feature-item:hover {
  box-shadow: var(--td-shadow-2);
  transform: translateY(-2px);
}

.feature-icon {
  font-size: 32px;
  margin-bottom: 12px;
}

.feature-item h4 {
  font-size: 16px;
  font-weight: 600;
  color: var(--td-text-color-primary);
  margin-bottom: 8px;
}

.feature-item p {
  font-size: 14px;
  color: var(--td-text-color-secondary);
  line-height: 1.5;
}
</style>
