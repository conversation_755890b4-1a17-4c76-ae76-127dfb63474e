/**
 * tdesign v1.14.2
 * (c) 2025 tdesign
 * @license MIT
 */

'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var props = {
  autofocus: <PERSON>olean,
  borderless: Boolean,
  clearable: <PERSON>olean,
  "default": {
    type: [String, Function]
  },
  disabled: {
    type: <PERSON><PERSON><PERSON>,
    "default": void 0
  },
  empty: {
    type: [String, Function]
  },
  filter: {
    type: Function
  },
  filterable: {
    type: Boolean,
    "default": true
  },
  highlightKeyword: {
    type: Boolean,
    "default": true
  },
  inputProps: {
    type: Object
  },
  options: {
    type: Array
  },
  panelBottomContent: {
    type: [String, Function]
  },
  panelTopContent: {
    type: [String, Function]
  },
  placeholder: {
    type: String,
    "default": void 0
  },
  popupProps: {
    type: Object
  },
  readonly: {
    type: Boolean,
    "default": void 0
  },
  size: {
    type: String,
    "default": "medium",
    validator: function validator(val) {
      if (!val) return true;
      return ["small", "medium", "large"].includes(val);
    }
  },
  status: {
    type: String,
    "default": "default",
    validator: function validator(val) {
      if (!val) return true;
      return ["default", "success", "warning", "error"].includes(val);
    }
  },
  textareaProps: {
    type: Object
  },
  tips: {
    type: [String, Function]
  },
  triggerElement: {
    type: [String, Function]
  },
  value: {
    type: String,
    "default": void 0
  },
  modelValue: {
    type: String,
    "default": void 0
  },
  defaultValue: {
    type: String,
    "default": ""
  },
  onBlur: Function,
  onChange: Function,
  onClear: Function,
  onCompositionend: Function,
  onCompositionstart: Function,
  onEnter: Function,
  onFocus: Function,
  onSelect: Function
};

exports["default"] = props;
//# sourceMappingURL=props.js.map
