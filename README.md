# 空间服务与应用预测系统

## 项目简介

空间服务与应用预测系统是一个基于Vue框架的前端应用系统，主要用于空间基准框架服务和多源数据应用与预测。系统采用CGCS2000坐标系统，集成本地Cesium三维地图，为用户提供专业的空间数据处理和预测分析服务。

## 主要功能模块

### 1. 空间基准框架服务
- **动态时空基准统一**：基于BDS/GNSS基准站设施的坐标时间序列分析
- **区域独立坐标系转换**：实现区域独立坐标系和CGCS2000的快速精确转换
- **国家高程基准统一**：构建高程基准转换模型，实现高程基准向国家高程基准的转换
- **极地高程基准统一**：基于极轨测高卫星观测数据的高程基准统一

### 2. 多源数据应用与预测
- **电离层监测与预测**：区域电离层TEC值提取、行扰分析和短期预报
- **对流层监测与分析**：对流层产品计算、可降水量建模反演评估
- **区域海平面变化预测**：基于卫星高度计观测数据的海平面变化预测

## 技术栈

- **前端框架**：Vue 3
- **UI组件库**：TDesign + Element UI
- **图表控件**：ECharts
- **地图组件**：Cesium（本地三维地图）
- **构建工具**：Vite
- **包管理器**：npm

## 项目结构

```
src/
├── web/                                    # Web端页面
│   ├── spatialFramework/                   # 空间基准框架服务
│   │   ├── dynamicUnification/             # 动态时空基准统一
│   │   ├── coordinateTransform/            # 区域独立坐标系转换
│   │   ├── elevationUnification/           # 国家高程基准统一
│   │   └── polarElevation/                 # 极地高程基准统一
│   └── dataApplication/                    # 多源数据应用与预测
│       ├── ionosphereMonitoring/           # 电离层监测与预测
│       ├── troposphereMonitoring/          # 对流层监测与分析
│       └── seaLevelPrediction/             # 区域海平面变化预测
├── components/                             # 公共组件
├── assets/                                 # 静态资源
│   ├── css/                               # 样式文件
│   ├── images/                            # 图片资源
│   └── icon/                              # 图标资源
├── router/                                 # 路由配置
├── store/                                  # 状态管理
└── utils/                                  # 工具函数
```

## 设计规范

### 布局规范
- **整体布局**：顶部导航栏（60px）+ 内容展示区（1020px）
- **子菜单样式**：tab样式显示，距左边距和顶部导航栏底部各间隔10px

### 配色规范
- **主色调**：#063078（深蓝色）
- **配色体系**：遵循TDesign设计规范
- **主题支持**：浅色模式和深色模式

### 命名规范
- **文件命名**：采用小驼峰法
- **页面命名**：
  - 首页：index.vue
  - 大屏/一张图：overView.vue
  - 图表看板：dashBoard.vue
  - 列表页：[模块名]List.vue
  - 详情页：[模块名]Detail.vue
  - 模态框：[功能名]Modal.vue

### 目录规范
- **层级划分**：平台-分系统-子系统-菜单-子菜单
- **特殊文件夹**：
  - Modal/：模态框内嵌页面
  - component/：公用组件
  - data/：页面模拟数据
  - echartsForm/：ECharts图表组件

## 地图配置

系统使用本地Cesium三维地图：
- **瓦片服务地址**：http://127.0.0.1:8091/{z4490}/{x}/{y}.jpg
- **坐标系统**：CGCS2000
- **初始位置**：经度112.40132，纬度34.624302
- **支持功能**：WebGL检测、备用方案

## 开发指南

### 环境要求
- Node.js >= 16.0.0
- npm >= 8.0.0

### 安装依赖
```bash
npm install
```

### 启动开发服务器
```bash
npm run dev
```

### 构建生产版本
```bash
npm run build
```

## 开发规范

1. **设计原则**：从大到小、从全局到单个功能递进式设计
2. **功能要求**：功能全面、逻辑严谨、思路清晰、交互合理
3. **代码规范**：遵循Vue 3 Composition API规范
4. **组件设计**：采用列表页+功能页+模态框的设计模式

## 版本信息

- **当前版本**：1.0.0
- **创建日期**：2025-07-14
- **维护状态**：开发中

## 联系方式

如有问题或建议，请联系开发团队。
