import { TdBaseTableProps } from '../type';
export default function useAffix(props: TdBaseTableProps): {
    showAffixHeader: import("vue").Ref<boolean>;
    showAffixFooter: import("vue").Ref<boolean>;
    showAffixPagination: import("vue").Ref<boolean>;
    affixHeaderRef: import("vue").Ref<HTMLDivElement>;
    affixFooterRef: import("vue").Ref<HTMLDivElement>;
    horizontalScrollbarRef: import("vue").Ref<HTMLDivElement>;
    paginationRef: import("vue").Ref<HTMLDivElement>;
    onHorizontalScroll: (scrollElement?: HTMLElement) => void;
    setTableContentRef: (tableContent: HTMLDivElement) => void;
    updateAffixHeaderOrFooter: () => void;
};
