<template>
  <div class="coordinate-transform-container">
    <!-- 子功能Tab菜单 -->
    <div class="sub-function-tabs">
      <t-tabs
        v-model="activeSubFunction"
        theme="normal"
        size="small"
        @change="handleSubFunctionChange"
      >
        <t-tab-panel
          v-for="subFunction in subFunctions"
          :key="subFunction.key"
          :value="subFunction.key"
          :label="subFunction.title"
        >
          <div class="sub-function-content">
            <component :is="subFunction.component" />
          </div>
        </t-tab-panel>
      </t-tabs>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, defineAsyncComponent } from 'vue'

// 当前激活的子功能
const activeSubFunction = ref('regional-definition')

// 子功能列表
const subFunctions = ref([
  {
    key: 'regional-definition',
    title: '区域坐标系定义',
    component: defineAsyncComponent(() => import('./component/RegionalDefinition.vue'))
  },
  {
    key: 'math-model',
    title: '数学转换模型',
    component: defineAsyncComponent(() => import('./component/MathModel.vue'))
  },
  {
    key: 'fast-transform',
    title: '快速精确转换',
    component: defineAsyncComponent(() => import('./component/FastTransform.vue'))
  },
  {
    key: 'unified-transform',
    title: '统一坐标系转换',
    component: defineAsyncComponent(() => import('./component/UnifiedTransform.vue'))
  }
])

// 子功能切换处理
const handleSubFunctionChange = (value) => {
  activeSubFunction.value = value
  console.log('切换到子功能:', value)
}

onMounted(() => {
  console.log('区域独立坐标系转换页面已加载')
})
</script>

<style scoped>
.coordinate-transform-container {
  width: 100%;
  height: 100%;
}

.sub-function-tabs {
  width: 100%;
  height: 100%;
}

.sub-function-content {
  padding: 16px;
  height: 100%;
  overflow-y: auto;
}

/* Tab样式调整 */
:deep(.t-tabs__nav) {
  background: transparent;
  border-bottom: 1px solid var(--td-border-level-1-color);
}

:deep(.t-tabs__content) {
  padding: 0;
}

:deep(.t-tab-panel) {
  height: 100%;
}
</style>
