<template>
  <div class="coordinate-transform-container">
    <div class="page-header">
      <h1 class="page-title">区域独立坐标系转换</h1>
      <p class="page-subtitle">针对复杂多样的部分区域坐标系统，实现区域独立坐标系和 CGCS2000的快速精确转换</p>
    </div>
    
    <div class="content-area">
      <div class="card">
        <div class="card-header">
          <h3>坐标系转换功能</h3>
        </div>
        <div class="card-body">
          <div class="transform-features">
            <div class="feature-item">
              <div class="feature-icon">🗺️</div>
              <h4>区域坐标系定义</h4>
              <p>利用部分区域的坐标系定义和控制点成果</p>
            </div>
            <div class="feature-item">
              <div class="feature-icon">🔢</div>
              <h4>数学转换模型</h4>
              <p>通过区域独立坐标系建立的数学方法和坐标系间转换模型</p>
            </div>
            <div class="feature-item">
              <div class="feature-icon">⚡</div>
              <h4>快速精确转换</h4>
              <p>实现区域独立坐标系和 CGCS2000的快速精确转换</p>
            </div>
            <div class="feature-item">
              <div class="feature-icon">🌐</div>
              <h4>统一坐标系转换</h4>
              <p>实现整体和部分区域的坐标系转换统一</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'

const pageData = ref({
  title: '区域独立坐标系转换',
  description: '针对复杂多样的部分区域坐标系统，实现区域独立坐标系和 CGCS2000的快速精确转换'
})

onMounted(() => {
  console.log('区域独立坐标系转换页面已加载')
})
</script>

<style scoped>
.coordinate-transform-container {
  padding: 24px;
  background: var(--td-bg-color-page);
  min-height: 100%;
}

.page-header {
  margin-bottom: 24px;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: var(--td-text-color-primary);
  margin-bottom: 8px;
}

.page-subtitle {
  font-size: 14px;
  color: var(--td-text-color-secondary);
  line-height: 1.6;
}

.transform-features {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
}

.feature-item {
  padding: 20px;
  background: var(--td-bg-color-container);
  border-radius: var(--td-radius-medium);
  border: 1px solid var(--td-border-level-1-color);
  text-align: center;
  transition: all 0.3s ease;
}

.feature-item:hover {
  box-shadow: var(--td-shadow-2);
  transform: translateY(-2px);
}

.feature-icon {
  font-size: 32px;
  margin-bottom: 12px;
}

.feature-item h4 {
  font-size: 16px;
  font-weight: 600;
  color: var(--td-text-color-primary);
  margin-bottom: 8px;
}

.feature-item p {
  font-size: 14px;
  color: var(--td-text-color-secondary);
  line-height: 1.5;
}
</style>
