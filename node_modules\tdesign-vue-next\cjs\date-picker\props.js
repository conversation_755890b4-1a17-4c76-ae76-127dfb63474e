/**
 * tdesign v1.14.2
 * (c) 2025 tdesign
 * @license MIT
 */

'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var datePickerProps = {
  allowInput: Boolean,
  borderless: Boolean,
  clearable: Boolean,
  defaultTime: {
    type: String,
    "default": "00:00:00"
  },
  disableDate: {
    type: [Object, Array, Function]
  },
  disabled: {
    type: Boolean,
    "default": void 0
  },
  enableTimePicker: Boolean,
  firstDayOfWeek: {
    type: Number,
    validator: function validator(val) {
      if (!val) return true;
      return [1, 2, 3, 4, 5, 6, 7].includes(val);
    }
  },
  format: {
    type: String,
    "default": void 0
  },
  inputProps: {
    type: Object
  },
  label: {
    type: [String, Function]
  },
  mode: {
    type: String,
    "default": "date",
    validator: function validator(val) {
      if (!val) return true;
      return ["year", "quarter", "month", "week", "date"].includes(val);
    }
  },
  multiple: <PERSON>olean,
  needConfirm: {
    type: Boolean,
    "default": true
  },
  placeholder: {
    type: [String, Array],
    "default": void 0
  },
  popupProps: {
    type: Object
  },
  prefixIcon: {
    type: Function
  },
  presets: {
    type: Object
  },
  presetsPlacement: {
    type: String,
    "default": "bottom",
    validator: function validator(val) {
      if (!val) return true;
      return ["left", "top", "right", "bottom"].includes(val);
    }
  },
  readonly: {
    type: Boolean,
    "default": void 0
  },
  selectInputProps: {
    type: Object
  },
  size: {
    type: String,
    "default": "medium",
    validator: function validator(val) {
      if (!val) return true;
      return ["small", "medium", "large"].includes(val);
    }
  },
  status: {
    type: String,
    "default": "default",
    validator: function validator(val) {
      if (!val) return true;
      return ["default", "success", "warning", "error"].includes(val);
    }
  },
  suffixIcon: {
    type: Function
  },
  timePickerProps: {
    type: Object
  },
  tips: {
    type: [String, Function]
  },
  value: {
    type: [String, Number, Array, Date],
    "default": void 0
  },
  modelValue: {
    type: [String, Number, Array, Date],
    "default": void 0
  },
  defaultValue: {
    type: [String, Number, Array, Date],
    "default": ""
  },
  valueDisplay: {
    type: [String, Function]
  },
  valueType: {
    type: String,
    "default": ""
  },
  onBlur: Function,
  onChange: Function,
  onConfirm: Function,
  onFocus: Function,
  onPick: Function,
  onPresetClick: Function
};

exports["default"] = datePickerProps;
//# sourceMappingURL=props.js.map
