import { CalendarState } from '../types';
import { TdCalendarProps } from '../type';
export declare function useCalendarClass(props: TdCalendarProps, state: CalendarState): {
    body: import("vue").ComputedRef<string[]>;
    panel: import("vue").ComputedRef<string[]>;
    control: import("vue").ComputedRef<string[]>;
    title: import("vue").ComputedRef<string[]>;
    controlSection: import("vue").ComputedRef<string[]>;
    controlSectionCell: import("vue").ComputedRef<string[]>;
    controlTag: import("vue").ComputedRef<string[]>;
    table: import("vue").ComputedRef<string[]>;
    tableHead: import("vue").ComputedRef<string[]>;
    tableHeadRow: import("vue").ComputedRef<string[]>;
    tableHeadCell: import("vue").ComputedRef<string[]>;
    tableBody: import("vue").ComputedRef<string[]>;
    tableBodyRow: import("vue").ComputedRef<string[]>;
};
export declare function useCalendarCellClass(): {
    tableBodyCell: import("vue").ComputedRef<string[]>;
    tableBodyCell4Now: import("vue").ComputedRef<string>;
    tableBodyCellDisplay: import("vue").ComputedRef<string[]>;
    tableBodyCellCsontent: import("vue").ComputedRef<string[]>;
};
