import { ComputedRef } from 'vue';
import type { TdBreadcrumbItemProps, TdBreadcrumbProps } from '../type';
import type { TNode } from '../../common';
export declare const useEllipsis: (props: TdBreadcrumbProps, getBreadcrumbItems: ComputedRef<TdBreadcrumbItemProps[]>, ellipsisContent: string | TNode) => {
    getDisplayItems: ComputedRef<(TdBreadcrumbItemProps | {
        content: string | ((h: typeof import("vue").h) => import("../..").TNodeReturnValue);
        disabled: boolean;
        isEllipsisItem: boolean;
    })[]>;
    getEllipsisItems: ComputedRef<{
        isLast: boolean;
        content?: string | TNode;
        default?: string | TNode;
        disabled?: boolean;
        href?: string;
        icon?: TNode;
        maxWidth?: string;
        replace?: boolean;
        router?: any;
        target?: "_blank" | "_self" | "_parent" | "_top";
        to?: string | import("..").Route;
        onClick?: (e: MouseEvent) => void;
    }[]>;
};
