/**
 * tdesign v1.14.2
 * (c) 2025 tdesign
 * @license MIT
 */

'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var props = {
  content: {
    type: [String, Function]
  },
  "default": {
    type: [String, Function]
  },
  disabled: {
    type: Boolean,
    "default": void 0
  },
  download: {
    type: [String, Boolean]
  },
  hover: {
    type: String,
    "default": "underline",
    validator: function validator(val) {
      if (!val) return true;
      return ["color", "underline"].includes(val);
    }
  },
  href: {
    type: String,
    "default": ""
  },
  prefixIcon: {
    type: Function
  },
  size: {
    type: String,
    "default": "medium",
    validator: function validator(val) {
      if (!val) return true;
      return ["small", "medium", "large"].includes(val);
    }
  },
  suffixIcon: {
    type: Function
  },
  target: {
    type: String,
    "default": ""
  },
  theme: {
    type: String,
    "default": "default",
    validator: function validator(val) {
      if (!val) return true;
      return ["default", "primary", "danger", "warning", "success"].includes(val);
    }
  },
  underline: Boolean,
  onClick: Function
};

exports["default"] = props;
//# sourceMappingURL=props.js.map
