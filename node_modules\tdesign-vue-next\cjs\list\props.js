/**
 * tdesign v1.14.2
 * (c) 2025 tdesign
 * @license MIT
 */

'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var props = {
  asyncLoading: {
    type: [String, Function]
  },
  footer: {
    type: [String, Function]
  },
  header: {
    type: [String, Function]
  },
  layout: {
    type: String,
    "default": "horizontal",
    validator: function validator(val) {
      if (!val) return true;
      return ["horizontal", "vertical"].includes(val);
    }
  },
  scroll: {
    type: Object
  },
  size: {
    type: String,
    "default": "medium",
    validator: function validator(val) {
      if (!val) return true;
      return ["small", "medium", "large"].includes(val);
    }
  },
  split: Boole<PERSON>,
  stripe: Boolean,
  onLoadMore: Function,
  onScroll: Function
};

exports["default"] = props;
//# sourceMappingURL=props.js.map
