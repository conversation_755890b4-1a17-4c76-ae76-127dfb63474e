/**
 * tdesign v1.14.2
 * (c) 2025 tdesign
 * @license MIT
 */

'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var props = {
  block: Boolean,
  content: {
    type: [String, Function]
  },
  "default": {
    type: [String, Function]
  },
  disabled: {
    type: <PERSON><PERSON>an,
    "default": void 0
  },
  form: {
    type: String,
    "default": void 0
  },
  ghost: <PERSON><PERSON><PERSON>,
  href: {
    type: String,
    "default": ""
  },
  icon: {
    type: Function
  },
  loading: Boolean,
  loadingProps: {
    type: Object
  },
  shape: {
    type: String,
    "default": "rectangle",
    validator: function validator(val) {
      if (!val) return true;
      return ["rectangle", "square", "round", "circle"].includes(val);
    }
  },
  size: {
    type: String,
    "default": "medium",
    validator: function validator(val) {
      if (!val) return true;
      return ["extra-small", "small", "medium", "large"].includes(val);
    }
  },
  suffix: {
    type: Function
  },
  tag: {
    type: String,
    validator: function validator(val) {
      if (!val) return true;
      return ["button", "a", "div"].includes(val);
    }
  },
  theme: {
    type: String,
    validator: function validator(val) {
      if (!val) return true;
      return ["default", "primary", "danger", "warning", "success"].includes(val);
    }
  },
  type: {
    type: String,
    "default": "button",
    validator: function validator(val) {
      if (!val) return true;
      return ["submit", "reset", "button"].includes(val);
    }
  },
  variant: {
    type: String,
    "default": "base",
    validator: function validator(val) {
      if (!val) return true;
      return ["base", "outline", "dashed", "text"].includes(val);
    }
  },
  onClick: Function
};

exports["default"] = props;
//# sourceMappingURL=props.js.map
