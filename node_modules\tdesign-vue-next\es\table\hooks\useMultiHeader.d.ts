import { BaseTableColumns, ThRowspanAndColspan } from '../types';
export declare function getNodeDepth(columns: BaseTableColumns, depthMap: Map<any, number>, depth?: number): number;
export declare function getChildrenNodeWidth(node: BaseTableColumns[0], count?: number): number;
export declare function getThRowspanAndColspan(columns: BaseTableColumns): {
    rowspanAndColspanMap: ThRowspanAndColspan;
    leafColumns: BaseTableColumns;
};
export declare function getThList(columns: BaseTableColumns): Array<BaseTableColumns>;
