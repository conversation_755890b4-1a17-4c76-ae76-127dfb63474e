import { Ref } from 'vue';
import { TdSelectProps } from '../type';
import { Styles } from '../../common';
export declare const usePanelVirtualScroll: (props: {
    scroll: TdSelectProps["scroll"];
    popupContentRef: Ref<HTMLElement>;
    options: Ref<TdSelectProps["options"]>;
}) => {
    trs: Map<any, any>;
    scrollHeight: import("vue").ComputedRef<any>;
    translateY: import("vue").ComputedRef<any>;
    visibleData: Ref<any[]>;
    handleRowMounted: () => void;
    isVirtual: import("vue").ComputedRef<boolean>;
    cursorStyle: import("vue").ComputedRef<Styles>;
    panelStyle: import("vue").ComputedRef<Styles>;
};
