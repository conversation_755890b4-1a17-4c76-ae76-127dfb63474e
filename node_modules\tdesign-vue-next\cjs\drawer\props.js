/**
 * tdesign v1.14.2
 * (c) 2025 tdesign
 * @license MIT
 */

'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var props = {
  attach: {
    type: [String, Function]
  },
  body: {
    type: [String, Function]
  },
  cancelBtn: {
    type: [String, Object, Function]
  },
  closeBtn: {
    type: [String, Boolean, Function]
  },
  closeOnEscKeydown: {
    type: Boolean,
    "default": void 0
  },
  closeOnOverlayClick: {
    type: Boolean,
    "default": void 0
  },
  confirmBtn: {
    type: [String, Object, Function]
  },
  "default": {
    type: [String, Function]
  },
  destroyOnClose: Boolean,
  drawerClassName: {
    type: String,
    "default": ""
  },
  footer: {
    type: [Boolean, Function],
    "default": true
  },
  header: {
    type: [String, Boolean, Function],
    "default": true
  },
  lazy: Boolean,
  mode: {
    type: String,
    "default": "overlay",
    validator: function validator(val) {
      if (!val) return true;
      return ["overlay", "push"].includes(val);
    }
  },
  placement: {
    type: String,
    "default": "right",
    validator: function validator(val) {
      if (!val) return true;
      return ["left", "right", "top", "bottom"].includes(val);
    }
  },
  preventScrollThrough: {
    type: Boolean,
    "default": true
  },
  showInAttachedElement: Boolean,
  showOverlay: {
    type: Boolean,
    "default": true
  },
  size: {
    type: String,
    "default": void 0
  },
  sizeDraggable: {
    type: [Boolean, Object],
    "default": false
  },
  visible: Boolean,
  zIndex: {
    type: Number
  },
  onBeforeClose: Function,
  onBeforeOpen: Function,
  onCancel: Function,
  onClose: Function,
  onCloseBtnClick: Function,
  onConfirm: Function,
  onEscKeydown: Function,
  onOverlayClick: Function,
  onSizeDragEnd: Function
};

exports["default"] = props;
//# sourceMappingURL=props.js.map
