//This file is automatically rebuilt by the Cesium build process.
export default "/**\n\
 * A built-in GLSL floating-point constant for converting degrees to radians.\n\
 *\n\
 * @alias czm_radiansPerDegree\n\
 * @glslConstant\n\
 *\n\
 * @see CesiumMath.RADIANS_PER_DEGREE\n\
 *\n\
 * @example\n\
 * // GLSL declaration\n\
 * const float czm_radiansPerDegree = ...;\n\
 *\n\
 * // Example\n\
 * float rad = czm_radiansPerDegree * deg;\n\
 */\n\
const float czm_radiansPerDegree = 0.017453292519943295;\n\
";
