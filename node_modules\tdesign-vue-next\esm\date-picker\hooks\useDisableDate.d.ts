import type { TdDatePickerProps, TdDateRangePickerProps } from '../type';
export interface disableDateProps {
    disableDate?: TdDatePickerProps['disableDate'] | TdDateRangePickerProps['disableDate'];
    format?: TdDatePickerProps['format'];
    mode?: TdDatePickerProps['mode'];
    start?: Date;
    end?: Date;
}
export declare function useDisableDate(props: disableDateProps): {
    disableDate: (value: Date) => boolean;
    minDate: Date;
    maxDate: Date;
};
