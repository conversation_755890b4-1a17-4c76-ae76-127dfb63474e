/**
 * tdesign v1.14.2
 * (c) 2025 tdesign
 * @license MIT
 */

'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var props = {
  ellipsis: {
    type: [String, Function]
  },
  itemsAfterCollapse: {
    type: Number,
    "default": void 0
  },
  itemsBeforeCollapse: {
    type: Number,
    "default": void 0
  },
  maxItemWidth: {
    type: String,
    "default": void 0
  },
  maxItems: {
    type: Number,
    "default": void 0
  },
  options: {
    type: Array
  },
  separator: {
    type: [String, Function]
  },
  theme: {
    type: String,
    "default": "light",
    validator: function validator(val) {
      if (!val) return true;
      return ["light"].includes(val);
    }
  }
};

exports["default"] = props;
//# sourceMappingURL=props.js.map
