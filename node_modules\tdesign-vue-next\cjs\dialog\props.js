/**
 * tdesign v1.14.2
 * (c) 2025 tdesign
 * @license MIT
 */

'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var props = {
  attach: {
    type: [String, Function]
  },
  body: {
    type: [String, Function]
  },
  cancelBtn: {
    type: [String, Object, Function, null]
  },
  closeBtn: {
    type: [String, Boolean, Function],
    "default": true
  },
  closeOnEscKeydown: {
    type: Boolean,
    "default": void 0
  },
  closeOnOverlayClick: {
    type: Boolean,
    "default": void 0
  },
  confirmBtn: {
    type: [String, Object, Function, null]
  },
  confirmLoading: {
    type: Boolean,
    "default": void 0
  },
  confirmOnEnter: Boolean,
  "default": {
    type: [String, Function]
  },
  destroyOnClose: Boolean,
  dialogClassName: {
    type: String,
    "default": ""
  },
  dialogStyle: {
    type: Object
  },
  draggable: Boolean,
  footer: {
    type: [<PERSON>olean, Function],
    "default": true
  },
  header: {
    type: [String, Boolean, Function],
    "default": true
  },
  lazy: Boolean,
  mode: {
    type: String,
    "default": "modal",
    validator: function validator(val) {
      if (!val) return true;
      return ["modal", "modeless", "normal", "full-screen"].includes(val);
    }
  },
  placement: {
    type: String,
    "default": "top",
    validator: function validator(val) {
      if (!val) return true;
      return ["top", "center"].includes(val);
    }
  },
  preventScrollThrough: {
    type: Boolean,
    "default": true
  },
  showInAttachedElement: Boolean,
  showOverlay: {
    type: Boolean,
    "default": true
  },
  theme: {
    type: String,
    "default": "default",
    validator: function validator(val) {
      if (!val) return true;
      return ["default", "info", "warning", "danger", "success"].includes(val);
    }
  },
  top: {
    type: [String, Number]
  },
  visible: Boolean,
  width: {
    type: [String, Number]
  },
  zIndex: {
    type: Number
  },
  onBeforeClose: Function,
  onBeforeOpen: Function,
  onCancel: Function,
  onClose: Function,
  onCloseBtnClick: Function,
  onClosed: Function,
  onConfirm: Function,
  onEscKeydown: Function,
  onOpened: Function,
  onOverlayClick: Function
};

exports["default"] = props;
//# sourceMappingURL=props.js.map
