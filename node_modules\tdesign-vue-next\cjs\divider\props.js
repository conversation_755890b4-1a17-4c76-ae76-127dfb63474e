/**
 * tdesign v1.14.2
 * (c) 2025 tdesign
 * @license MIT
 */

'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var props = {
  align: {
    type: String,
    "default": "center",
    validator: function validator(val) {
      if (!val) return true;
      return ["left", "right", "center"].includes(val);
    }
  },
  content: {
    type: [String, Function]
  },
  dashed: Boolean,
  "default": {
    type: [String, Function]
  },
  layout: {
    type: String,
    "default": "horizontal",
    validator: function validator(val) {
      if (!val) return true;
      return ["horizontal", "vertical"].includes(val);
    }
  },
  theme: {
    type: String,
    validator: function validator(val) {
      if (!val) return true;
      return ["horizontal", "vertical"].includes(val);
    }
  }
};

exports["default"] = props;
//# sourceMappingURL=props.js.map
