{"version": 3, "file": "alert.mjs", "sources": ["../../../../../../packages/components/alert/src/alert.ts"], "sourcesContent": ["import { useDelayedToggleProps } from '@element-plus/hooks'\nimport {\n  TypeComponentsMap,\n  buildProps,\n  isUndefined,\n  keysOf,\n} from '@element-plus/utils'\n\nimport type { ExtractPropTypes, __ExtractPublicPropTypes } from 'vue'\n\nexport const alertEffects = ['light', 'dark'] as const\n\nexport const alertProps = buildProps({\n  /**\n   * @description alert title.\n   */\n  title: {\n    type: String,\n    default: '',\n  },\n  description: {\n    type: String,\n    default: '',\n  },\n  /**\n   * @description alert type.\n   */\n  type: {\n    type: String,\n    values: keysOf(TypeComponentsMap),\n    default: 'info',\n  },\n  /**\n   * @description whether alert can be dismissed.\n   */\n  closable: {\n    type: Boolean,\n    default: true,\n  },\n  /**\n   * @description text for replacing x button\n   */\n  closeText: {\n    type: String,\n    default: '',\n  },\n  /**\n   * @description whether show icon\n   */\n  showIcon: Boolean,\n  /**\n   * @description should content be placed in center.\n   */\n  center: Boolean,\n  effect: {\n    type: String,\n    values: alertEffects,\n    default: 'light',\n  },\n  ...useDelayedToggleProps,\n} as const)\nexport type AlertProps = ExtractPropTypes<typeof alertProps>\nexport type AlertPropsPublic = __ExtractPublicPropTypes<typeof alertProps>\n\nexport const alertEmits = {\n  open: () => true,\n  close: (evt?: Event) => isUndefined(evt) || evt instanceof Event,\n}\nexport type AlertEmits = typeof alertEmits\n"], "names": [], "mappings": ";;;;;;AAOY,MAAC,YAAY,GAAG,CAAC,OAAO,EAAE,MAAM,EAAE;AAClC,MAAC,UAAU,GAAG,UAAU,CAAC;AACrC,EAAE,KAAK,EAAE;AACT,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,EAAE;AACf,GAAG;AACH,EAAE,WAAW,EAAE;AACf,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,EAAE;AACf,GAAG;AACH,EAAE,IAAI,EAAE;AACR,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,MAAM,EAAE,MAAM,CAAC,iBAAiB,CAAC;AACrC,IAAI,OAAO,EAAE,MAAM;AACnB,GAAG;AACH,EAAE,QAAQ,EAAE;AACZ,IAAI,IAAI,EAAE,OAAO;AACjB,IAAI,OAAO,EAAE,IAAI;AACjB,GAAG;AACH,EAAE,SAAS,EAAE;AACb,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,EAAE;AACf,GAAG;AACH,EAAE,QAAQ,EAAE,OAAO;AACnB,EAAE,MAAM,EAAE,OAAO;AACjB,EAAE,MAAM,EAAE;AACV,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,MAAM,EAAE,YAAY;AACxB,IAAI,OAAO,EAAE,OAAO;AACpB,GAAG;AACH,EAAE,GAAG,qBAAqB;AAC1B,CAAC,EAAE;AACS,MAAC,UAAU,GAAG;AAC1B,EAAE,IAAI,EAAE,MAAM,IAAI;AAClB,EAAE,KAAK,EAAE,CAAC,GAAG,KAAK,WAAW,CAAC,GAAG,CAAC,IAAI,GAAG,YAAY,KAAK;AAC1D;;;;"}