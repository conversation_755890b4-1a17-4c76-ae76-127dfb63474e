/**
 * tdesign v1.14.2
 * (c) 2025 tdesign
 * @license MIT
 */

'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var props = {
  closeBtn: {
    type: [String, Boolean, Function],
    "default": void 0
  },
  content: {
    type: [String, Function]
  },
  duration: {
    type: Number,
    "default": 3e3
  },
  icon: {
    type: [<PERSON>olean, Function],
    "default": true
  },
  theme: {
    type: String,
    "default": "info",
    validator: function validator(val) {
      if (!val) return true;
      return ["info", "success", "warning", "error", "question", "loading"].includes(val);
    }
  },
  onClose: Function,
  onCloseBtnClick: Function,
  onDurationEnd: Function
};

exports["default"] = props;
//# sourceMappingURL=props.js.map
