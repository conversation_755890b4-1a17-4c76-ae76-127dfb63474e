/**
 * tdesign v1.14.2
 * (c) 2025 tdesign
 * @license MIT
 */

'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var props = {
  autofocus: <PERSON><PERSON><PERSON>,
  borderless: <PERSON><PERSON>an,
  checkProps: {
    type: Object
  },
  checkStrictly: <PERSON><PERSON><PERSON>,
  clearable: Boolean,
  collapsedItems: {
    type: Function
  },
  disabled: {
    type: Boolean,
    "default": void 0
  },
  empty: {
    type: [String, Function]
  },
  filter: {
    type: Function
  },
  filterable: Boolean,
  inputProps: {
    type: Object
  },
  keys: {
    type: Object
  },
  label: {
    type: [String, Function]
  },
  lazy: {
    type: <PERSON>olean,
    "default": true
  },
  load: {
    type: Function
  },
  loading: Boolean,
  loadingText: {
    type: [String, Function]
  },
  max: {
    type: Number,
    "default": 0
  },
  minCollapsedNum: {
    type: Number,
    "default": 0
  },
  multiple: Boolean,
  option: {
    type: Function
  },
  options: {
    type: Array,
    "default": function _default() {
      return [];
    }
  },
  panelBottomContent: {
    type: [String, Function]
  },
  panelTopContent: {
    type: [String, Function]
  },
  placeholder: {
    type: String,
    "default": void 0
  },
  popupProps: {
    type: Object
  },
  popupVisible: Boolean,
  prefixIcon: {
    type: Function
  },
  readonly: {
    type: Boolean,
    "default": void 0
  },
  reserveKeyword: Boolean,
  selectInputProps: {
    type: Object
  },
  showAllLevels: {
    type: Boolean,
    "default": true
  },
  size: {
    type: String,
    "default": "medium",
    validator: function validator(val) {
      if (!val) return true;
      return ["large", "medium", "small"].includes(val);
    }
  },
  status: {
    type: String,
    "default": "default",
    validator: function validator(val) {
      if (!val) return true;
      return ["default", "success", "warning", "error"].includes(val);
    }
  },
  suffix: {
    type: [String, Function]
  },
  suffixIcon: {
    type: Function
  },
  tagInputProps: {
    type: Object
  },
  tagProps: {
    type: Object
  },
  tips: {
    type: [String, Function]
  },
  trigger: {
    type: String,
    "default": "click",
    validator: function validator(val) {
      if (!val) return true;
      return ["click", "hover"].includes(val);
    }
  },
  value: {
    type: [String, Number, Array],
    "default": void 0
  },
  modelValue: {
    type: [String, Number, Array],
    "default": void 0
  },
  defaultValue: {
    type: [String, Number, Array],
    "default": function _default() {
      return [];
    }
  },
  valueDisplay: {
    type: [String, Function]
  },
  valueMode: {
    type: String,
    "default": "onlyLeaf",
    validator: function validator(val) {
      if (!val) return true;
      return ["onlyLeaf", "parentFirst", "all"].includes(val);
    }
  },
  valueType: {
    type: String,
    "default": "single",
    validator: function validator(val) {
      if (!val) return true;
      return ["single", "full"].includes(val);
    }
  },
  onBlur: Function,
  onChange: Function,
  onFocus: Function,
  onPopupVisibleChange: Function,
  onRemove: Function
};

exports["default"] = props;
//# sourceMappingURL=props.js.map
