/**
 * tdesign v1.14.2
 * (c) 2025 tdesign
 * @license MIT
 */

'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var props = {
  affixProps: {
    type: Object
  },
  bounds: {
    type: Number,
    "default": 5
  },
  container: {
    type: [String, Function],
    "default": function _default() {
      return function () {
        return window;
      };
    }
  },
  cursor: {
    type: Function
  },
  size: {
    type: String,
    "default": "medium",
    validator: function validator(val) {
      return ["small", "medium", "large"].includes(val);
    }
  },
  targetOffset: {
    type: Number,
    "default": 0
  },
  onChange: Function,
  onClick: Function
};

exports["default"] = props;
//# sourceMappingURL=props.js.map
