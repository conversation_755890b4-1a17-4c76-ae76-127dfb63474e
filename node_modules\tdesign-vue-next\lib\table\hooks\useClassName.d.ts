export default function useClassName(): {
    classPrefix: string;
    tableBaseClass: {
        table: string;
        columnResizableTable: string;
        overflowVisible: string;
        body: string;
        content: string;
        topContent: string;
        bottomContent: string;
        paginationWrap: string;
        tdLastRow: string;
        tdFirstCol: string;
        thCellInner: string;
        tableRowEdit: string;
        cellEditable: string;
        cellEditWrap: string;
        bordered: string;
        striped: string;
        hover: string;
        loading: string;
        rowspanAndColspan: string;
        empty: string;
        emptyRow: string;
        headerFixed: string;
        columnFixed: string;
        widthOverflow: string;
        multipleHeader: string;
        footerAffixed: string;
        horizontalBarAffixed: string;
        affixedHeader: string;
        affixedHeaderElm: string;
        affixedFooterElm: string;
        affixedFooterWrap: string;
        scrollbarDivider: string;
        fullHeight: string;
        resizeLine: string;
        obviousScrollbar: string;
        affixedHeaderWrap: string;
    };
    tdAlignClasses: {
        left: string;
        right: string;
        center: string;
    };
    tableHeaderClasses: {
        header: string;
        thBordered: string;
        fixed: string;
        multipleHeader: string;
    };
    tableFooterClasses: {
        footer: string;
        fixed: string;
    };
    tableAlignClasses: {
        top: string;
        middle: string;
        bottom: string;
    };
    tableRowFixedClasses: {
        top: string;
        bottom: string;
        firstBottom: string;
        withoutBorderBottom: string;
    };
    tableColFixedClasses: {
        left: string;
        right: string;
        lastLeft: string;
        firstRight: string;
        leftShadow: string;
        rightShadow: string;
    };
    tableLayoutClasses: {
        auto: string;
        fixed: string;
    };
    tdEllipsisClass: string;
    tableFullRowClasses: {
        base: string;
        innerFullRow: string;
        innerFullElement: string;
        firstFullRow: string;
        lastFullRow: string;
    };
    tableExpandClasses: {
        iconBox: string;
        iconCell: string;
        rowExpanded: string;
        rowFolded: string;
        row: string;
        rowInner: string;
        expanded: string;
        collapsed: string;
    };
    tableSortClasses: {
        sortable: string;
        sortColumn: string;
        title: string;
        trigger: string;
        doubleIcon: string;
        sortIcon: string;
        iconDirection: {
            asc: string;
            desc: string;
        };
        iconActive: string;
        iconDefault: string;
    };
    tableSelectedClasses: {
        selected: string;
        disabled: string;
        checkCell: string;
    };
    tableFilterClasses: {
        filterable: string;
        popup: string;
        icon: string;
        popupContent: string;
        result: string;
        inner: string;
        bottomButtons: string;
        contentInner: string;
        iconWrap: string;
    };
    asyncLoadingClass: string;
    isFocusClass: string;
    isLoadingClass: string;
    isLoadMoreClass: string;
    tableTreeClasses: {
        col: string;
        inlineCol: string;
        icon: string;
        leafNode: string;
    };
    tableDraggableClasses: {
        rowDraggable: string;
        rowHandlerDraggable: string;
        colDraggable: string;
        handle: string;
        ghost: string;
        chosen: string;
        dragging: string;
        dragSortTh: string;
    };
    virtualScrollClasses: {
        cursor: string;
        header: string;
    };
    positiveRotate90: string;
    negativeRotate180: string;
};
export type TableClassName = ReturnType<typeof useClassName>;
