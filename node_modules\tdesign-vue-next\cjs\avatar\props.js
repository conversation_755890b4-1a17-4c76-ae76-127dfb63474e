/**
 * tdesign v1.14.2
 * (c) 2025 tdesign
 * @license MIT
 */

'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var props = {
  alt: {
    type: String,
    "default": ""
  },
  content: {
    type: [String, Function]
  },
  "default": {
    type: [String, Function]
  },
  hideOnLoadFailed: <PERSON><PERSON><PERSON>,
  icon: {
    type: Function
  },
  image: {
    type: String,
    "default": ""
  },
  imageProps: {
    type: Object
  },
  shape: {
    type: String,
    "default": "circle",
    validator: function validator(val) {
      if (!val) return true;
      return ["circle", "round"].includes(val);
    }
  },
  size: {
    type: String,
    "default": ""
  },
  onError: Function
};

exports["default"] = props;
//# sourceMappingURL=props.js.map
