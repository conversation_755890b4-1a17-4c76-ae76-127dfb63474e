<template>
  <div class="elevation-unification-container">
    <div class="page-header">
      <h1 class="page-title">国家高程基准统一</h1>
      <p class="page-subtitle">针对高程基准统一，构建高程基准转换模型，实现特定区域 GNSS/水准数据高程基准向国家高程基准的转换</p>
    </div>
    
    <div class="content-area">
      <div class="card">
        <div class="card-header">
          <h3>高程基准统一功能</h3>
        </div>
        <div class="card-body">
          <div class="elevation-features">
            <div class="feature-item">
              <div class="feature-icon">🌍</div>
              <h4>地球重力场模型</h4>
              <p>利用已有地球重力场模型和可收集的特定区域的重力异常</p>
            </div>
            <div class="feature-item">
              <div class="feature-icon">📊</div>
              <h4>GNSS/水准数据</h4>
              <p>特定区域高程基准中的GNSS/水准数据、地形数据与我国GNSS/水准数据</p>
            </div>
            <div class="feature-item">
              <div class="feature-icon">🔄</div>
              <h4>高程基准转换模型</h4>
              <p>构建高程基准转换模型</p>
            </div>
            <div class="feature-item">
              <div class="feature-icon">🎯</div>
              <h4>国家高程基准转换</h4>
              <p>实现特定区域 GNSS/水准数据高程基准向国家高程基准的转换</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'

const pageData = ref({
  title: '国家高程基准统一',
  description: '针对高程基准统一，构建高程基准转换模型'
})

onMounted(() => {
  console.log('国家高程基准统一页面已加载')
})
</script>

<style scoped>
.elevation-unification-container {
  padding: 24px;
  background: var(--td-bg-color-page);
  min-height: 100%;
}

.page-header {
  margin-bottom: 24px;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: var(--td-text-color-primary);
  margin-bottom: 8px;
}

.page-subtitle {
  font-size: 14px;
  color: var(--td-text-color-secondary);
  line-height: 1.6;
}

.elevation-features {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
}

.feature-item {
  padding: 20px;
  background: var(--td-bg-color-container);
  border-radius: var(--td-radius-medium);
  border: 1px solid var(--td-border-level-1-color);
  text-align: center;
  transition: all 0.3s ease;
}

.feature-item:hover {
  box-shadow: var(--td-shadow-2);
  transform: translateY(-2px);
}

.feature-icon {
  font-size: 32px;
  margin-bottom: 12px;
}

.feature-item h4 {
  font-size: 16px;
  font-weight: 600;
  color: var(--td-text-color-primary);
  margin-bottom: 8px;
}

.feature-item p {
  font-size: 14px;
  color: var(--td-text-color-secondary);
  line-height: 1.5;
}
</style>
