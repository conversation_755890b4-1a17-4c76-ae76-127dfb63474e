<template>
  <div class="elevation-unification-container">
    <!-- 子功能Tab菜单 -->
    <div class="sub-function-tabs">
      <t-tabs
        v-model="activeSubFunction"
        theme="normal"
        size="small"
        @change="handleSubFunctionChange"
      >
        <t-tab-panel
          v-for="subFunction in subFunctions"
          :key="subFunction.key"
          :value="subFunction.key"
          :label="subFunction.title"
        >
          <div class="sub-function-content">
            <component :is="subFunction.component" />
          </div>
        </t-tab-panel>
      </t-tabs>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, defineAsyncComponent } from 'vue'

// 当前激活的子功能
const activeSubFunction = ref('gravity-model')

// 子功能列表
const subFunctions = ref([
  {
    key: 'gravity-model',
    title: '地球重力场模型',
    component: defineAsyncComponent(() => import('./component/GravityModel.vue'))
  },
  {
    key: 'gnss-data',
    title: 'GNSS/水准数据',
    component: defineAsyncComponent(() => import('./component/GnssData.vue'))
  },
  {
    key: 'elevation-model',
    title: '高程基准转换模型',
    component: defineAsyncComponent(() => import('./component/ElevationModel.vue'))
  },
  {
    key: 'national-transform',
    title: '国家高程基准转换',
    component: defineAsyncComponent(() => import('./component/NationalTransform.vue'))
  }
])

// 子功能切换处理
const handleSubFunctionChange = (value) => {
  activeSubFunction.value = value
  console.log('切换到子功能:', value)
}

onMounted(() => {
  console.log('国家高程基准统一页面已加载')
})
</script>

<style scoped>
.elevation-unification-container {
  padding: 24px;
  background: var(--td-bg-color-page);
  min-height: 100%;
}

.page-header {
  margin-bottom: 24px;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: var(--td-text-color-primary);
  margin-bottom: 8px;
}

.page-subtitle {
  font-size: 14px;
  color: var(--td-text-color-secondary);
  line-height: 1.6;
}

.elevation-features {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
}

.feature-item {
  padding: 20px;
  background: var(--td-bg-color-container);
  border-radius: var(--td-radius-medium);
  border: 1px solid var(--td-border-level-1-color);
  text-align: center;
  transition: all 0.3s ease;
}

.feature-item:hover {
  box-shadow: var(--td-shadow-2);
  transform: translateY(-2px);
}

.feature-icon {
  font-size: 32px;
  margin-bottom: 12px;
}

.feature-item h4 {
  font-size: 16px;
  font-weight: 600;
  color: var(--td-text-color-primary);
  margin-bottom: 8px;
}

.feature-item p {
  font-size: 14px;
  color: var(--td-text-color-secondary);
  line-height: 1.5;
}
</style>
