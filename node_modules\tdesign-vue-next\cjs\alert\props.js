/**
 * tdesign v1.14.2
 * (c) 2025 tdesign
 * @license MIT
 */

'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var props = {
  close: {
    type: [String, Boolean, Function],
    "default": false
  },
  closeBtn: {
    type: [String, Boolean, Function],
    "default": false
  },
  "default": {
    type: [String, Function]
  },
  icon: {
    type: Function
  },
  maxLine: {
    type: Number,
    "default": 0
  },
  message: {
    type: [String, Function]
  },
  operation: {
    type: Function
  },
  theme: {
    type: String,
    "default": "info",
    validator: function validator(val) {
      if (!val) return true;
      return ["success", "info", "warning", "error"].includes(val);
    }
  },
  title: {
    type: [String, Function]
  },
  onClose: Function,
  onClosed: Function
};

exports["default"] = props;
//# sourceMappingURL=props.js.map
