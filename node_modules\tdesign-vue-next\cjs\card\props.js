/**
 * tdesign v1.14.2
 * (c) 2025 tdesign
 * @license MIT
 */

'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var props = {
  actions: {
    type: [String, Function]
  },
  avatar: {
    type: [String, Function]
  },
  bordered: {
    type: Boolean,
    "default": true
  },
  content: {
    type: [String, Function]
  },
  cover: {
    type: [String, Function]
  },
  "default": {
    type: [String, Function]
  },
  description: {
    type: [String, Function]
  },
  footer: {
    type: [String, Function]
  },
  header: {
    type: [String, Function]
  },
  headerBordered: Boolean,
  hoverShadow: Boolean,
  loading: {
    type: [Boolean, Function],
    "default": false
  },
  loadingProps: {
    type: Object
  },
  shadow: Boolean,
  size: {
    type: String,
    "default": "medium",
    validator: function validator(val) {
      if (!val) return true;
      return ["medium", "small"].includes(val);
    }
  },
  status: {
    type: String,
    "default": ""
  },
  subtitle: {
    type: [String, Function]
  },
  theme: {
    type: String,
    "default": "normal",
    validator: function validator(val) {
      if (!val) return true;
      return ["normal", "poster1", "poster2"].includes(val);
    }
  },
  title: {
    type: [String, Function]
  }
};

exports["default"] = props;
//# sourceMappingURL=props.js.map
