/**
 * tdesign v1.14.2
 * (c) 2025 tdesign
 * @license MIT
 */

'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var props = {
  actions: {
    type: Array
  },
  author: {
    type: [String, Function]
  },
  avatar: {
    type: [String, Object, Function]
  },
  content: {
    type: [String, Function]
  },
  datetime: {
    type: [String, Function]
  },
  quote: {
    type: [String, Function]
  },
  reply: {
    type: [String, Function]
  }
};

exports["default"] = props;
//# sourceMappingURL=props.js.map
