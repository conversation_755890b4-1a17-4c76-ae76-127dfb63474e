/**
 * tdesign v1.14.2
 * (c) 2025 tdesign
 * @license MIT
 */

'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var props = {
  borderless: Boolean,
  defaultExpandAll: Boolean,
  disabled: Boolean,
  expandIcon: {
    type: [Bo<PERSON>an, Function],
    "default": true
  },
  expandIconPlacement: {
    type: String,
    "default": "left",
    validator: function validator(val) {
      if (!val) return true;
      return ["left", "right"].includes(val);
    }
  },
  expandMutex: Boolean,
  expandOnRowClick: {
    type: Boolean,
    "default": true
  },
  value: {
    type: Array,
    "default": void 0
  },
  modelValue: {
    type: Array,
    "default": void 0
  },
  defaultValue: {
    type: Array
  },
  onChange: Function
};

exports["default"] = props;
//# sourceMappingURL=props.js.map
