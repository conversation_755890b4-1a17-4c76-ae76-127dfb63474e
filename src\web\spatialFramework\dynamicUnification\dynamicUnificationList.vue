<template>
  <div class="dynamic-unification-container">
    <div class="page-header">
      <h1 class="page-title">动态时空基准统一</h1>
      <p class="page-subtitle">基于整体分布的 BDS/GNSS 基准站设施，开展基准站坐标时间序列分析</p>
    </div>
    
    <div class="content-area">
      <div class="card">
        <div class="card-header">
          <h3>功能模块</h3>
        </div>
        <div class="card-body">
          <div class="feature-grid">
            <div class="feature-item">
              <div class="feature-icon">📡</div>
              <h4>基准站坐标时间序列分析</h4>
              <p>基于整体分布的 BDS/GNSS 基准站设施进行坐标时间序列分析</p>
            </div>
            <div class="feature-item">
              <div class="feature-icon">🌍</div>
              <h4>公共高精度板块速度场模型</h4>
              <p>构建公共高精度板块速度场模型，解决地心坐标系 CGCS2000线性动态精化难题</p>
            </div>
            <div class="feature-item">
              <div class="feature-icon">🔄</div>
              <h4>动态更新时空基准模型</h4>
              <p>建立整体融合、动态更新、实时精准的公共时空基准模型框架转换模型</p>
            </div>
            <div class="feature-item">
              <div class="feature-icon">📏</div>
              <h4>厘米级坐标框架转换</h4>
              <p>实现公共不同历元坐标框架的厘米级转换</p>
            </div>
          </div>
        </div>
      </div>
      
      <div class="card mt-20">
        <div class="card-header">
          <h3>系统状态</h3>
        </div>
        <div class="card-body">
          <div class="status-info">
            <div class="status-item">
              <span class="status-label">系统状态：</span>
              <span class="status-success">正常运行</span>
            </div>
            <div class="status-item">
              <span class="status-label">最后更新：</span>
              <span>2025-01-14 18:30:00</span>
            </div>
            <div class="status-item">
              <span class="status-label">数据源：</span>
              <span>BDS/GNSS基准站</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'

// 页面数据
const pageData = ref({
  title: '动态时空基准统一',
  description: '基于整体分布的 BDS/GNSS 基准站设施，开展基准站坐标时间序列分析'
})

onMounted(() => {
  console.log('动态时空基准统一页面已加载')
})
</script>

<style scoped>
.dynamic-unification-container {
  padding: 24px;
  background: var(--td-bg-color-page);
  min-height: 100%;
}

.page-header {
  margin-bottom: 24px;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: var(--td-text-color-primary);
  margin-bottom: 8px;
}

.page-subtitle {
  font-size: 14px;
  color: var(--td-text-color-secondary);
  line-height: 1.6;
}

.content-area {
  max-width: 1200px;
}

.feature-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
}

.feature-item {
  padding: 20px;
  background: var(--td-bg-color-container);
  border-radius: var(--td-radius-medium);
  border: 1px solid var(--td-border-level-1-color);
  text-align: center;
  transition: all 0.3s ease;
}

.feature-item:hover {
  box-shadow: var(--td-shadow-2);
  transform: translateY(-2px);
}

.feature-icon {
  font-size: 32px;
  margin-bottom: 12px;
}

.feature-item h4 {
  font-size: 16px;
  font-weight: 600;
  color: var(--td-text-color-primary);
  margin-bottom: 8px;
}

.feature-item p {
  font-size: 14px;
  color: var(--td-text-color-secondary);
  line-height: 1.5;
}

.status-info {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.status-label {
  font-weight: 500;
  color: var(--td-text-color-primary);
  min-width: 80px;
}

.status-success {
  color: var(--td-success-color);
  background: var(--td-success-color-light);
  padding: 2px 8px;
  border-radius: var(--td-radius-small);
  font-size: 12px;
}

@media (max-width: 768px) {
  .dynamic-unification-container {
    padding: 16px;
  }
  
  .feature-grid {
    grid-template-columns: 1fr;
  }
  
  .page-title {
    font-size: 20px;
  }
}
</style>
