<template>
  <div class="dynamic-unification-container">
    <!-- 子功能Tab菜单 -->
    <div class="sub-function-tabs">
      <t-tabs
        v-model="activeSubFunction"
        theme="normal"
        size="small"
        @change="handleSubFunctionChange"
      >
        <t-tab-panel
          v-for="subFunction in subFunctions"
          :key="subFunction.key"
          :value="subFunction.key"
          :label="subFunction.title"
        >
          <div class="sub-function-content">
            <component :is="subFunction.component" />
          </div>
        </t-tab-panel>
      </t-tabs>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, defineAsyncComponent } from 'vue'

// 当前激活的子功能
const activeSubFunction = ref('coordinate-analysis')

// 子功能列表
const subFunctions = ref([
  {
    key: 'coordinate-analysis',
    title: '基准站坐标时间序列分析',
    component: defineAsyncComponent(() => import('./component/CoordinateAnalysis.vue'))
  },
  {
    key: 'velocity-model',
    title: '公共高精度板块速度场模型',
    component: defineAsyncComponent(() => import('./component/VelocityModel.vue'))
  },
  {
    key: 'dynamic-model',
    title: '动态更新时空基准模型',
    component: defineAsyncComponent(() => import('./component/DynamicModel.vue'))
  },
  {
    key: 'coordinate-transform',
    title: '厘米级坐标框架转换',
    component: defineAsyncComponent(() => import('./component/CoordinateTransform.vue'))
  }
])

// 子功能切换处理
const handleSubFunctionChange = (value) => {
  activeSubFunction.value = value
  console.log('切换到子功能:', value)
}

onMounted(() => {
  console.log('动态时空基准统一页面已加载')
})
</script>

<style scoped>
.dynamic-unification-container {
  width: 100%;
  height: 100%;
}

.sub-function-tabs {
  width: 100%;
  height: 100%;
}

.sub-function-content {
  padding: 16px;
  height: 100%;
  overflow-y: auto;
}

/* Tab样式调整 */
:deep(.t-tabs__nav) {
  background: transparent;
  border-bottom: 1px solid var(--td-border-level-1-color);
}

:deep(.t-tabs__content) {
  padding: 0;
}

:deep(.t-tab-panel) {
  height: 100%;
}
</style>
